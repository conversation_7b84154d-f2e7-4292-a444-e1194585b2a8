{"name": "wto-trade-compliance", "version": "1.0.0", "exports": "./server.ts", "tasks": {"dev": "deno run --allow-net --allow-read --allow-write --allow-env --watch server.ts", "start": "deno run --allow-net --allow-read --allow-write --allow-env server.ts", "test": "deno test --allow-net --allow-read --allow-write --allow-env", "lint": "deno lint", "fmt": "deno fmt", "typecheck": "deno check server.ts"}, "imports": {"@std/assert": "jsr:@std/assert@1", "@std/http": "jsr:@std/http@1", "@std/path": "jsr:@std/path@1", "@std/fs": "jsr:@std/fs@1", "@std/crypto": "jsr:@std/crypto@1", "@std/datetime": "jsr:@std/datetime@1", "@std/testing": "jsr:@std/testing@1", "@std/media-types": "jsr:@std/media-types@1", "ts-pattern": "https://esm.sh/ts-pattern@5.0.5"}, "compilerOptions": {"allowJs": false, "lib": ["deno.window"], "strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "useUnknownInCatchVariables": true}, "fmt": {"useTabs": false, "lineWidth": 80, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "preserve"}, "lint": {"include": ["src/", "server.ts"], "exclude": ["build/", "dist/", "static/"]}}