// WTO Compliance Monitor - Functional Server Entry Point

import { createRouter, get, createJsonResponse } from "./src/lib/router.ts";
import { createApiRoutes } from "./src/server/routes.ts";
import { createCrawlingRoutes } from "./src/server/crawl-routes.ts";
import { createServices, createServicesWithFirecrawl } from "./src/server/dependencies.ts";
import { join } from "@std/path";

const createWTOComplianceServer = async () => {
  // Try to load Firecrawl API key from environment
  const firecrawlApiKey = Deno.env.get("FIRECRAWL_API_KEY");
  
  let services;
  if (firecrawlApiKey) {
    console.log("🔥 Initializing with Firecrawl support...");
    services = await createServicesWithFirecrawl({ firecrawlApiKey });
  } else {
    console.log("⚠️  Firecrawl API key not found. Crawling features disabled.");
    console.log("   Set FIRECRAWL_API_KEY environment variable to enable web crawling.");
    services = createServices();
  }
  
  const apiRoutes = createApiRoutes(services);
  const crawlingRoutes = createCrawlingRoutes(services);
  
  const routes = [
    // Health check
    get("/health", async () => {
      return createJsonResponse({
        status: "healthy",
        timestamp: new Date().toISOString(),
        version: "1.0.0",
      });
    }),
    
    // Home page
    get("/", async () => {
      try {
        const indexPath = join(Deno.cwd(), "src", "ui", "index.html");
        const content = await Deno.readTextFile(indexPath);
        return new Response(content, {
          headers: { "Content-Type": "text/html" },
        });
      } catch {
        return new Response("Welcome to WTO Compliance Monitor", {
          headers: { "Content-Type": "text/plain" },
        });
      }
    }),
    
    // API routes
    ...apiRoutes,
    
    // Crawling routes (if Firecrawl is available)
    ...crawlingRoutes,
  ];

  return createRouter(routes, {
    staticDir: join(Deno.cwd(), "src", "ui"),
    cors: {
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
      headers: ["Content-Type", "Authorization", "HX-Request"],
    },
  });
};

async function main() {
  console.log("🌍 Initializing WTO Trade Compliance Monitoring System...");
  
  try {
    const router = await createWTOComplianceServer();
    const port = parseInt(Deno.env.get("PORT") ?? "8000");
    
    console.log(`🚀 Server starting on port ${port}`);
    console.log(`📱 Dashboard available at: http://localhost:${port}`);
    console.log(`🔍 Health check: http://localhost:${port}/health`);
    
    await Deno.serve({ port }, router);
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    Deno.exit(1);
  }
}

if (import.meta.main) {
  await main();
}