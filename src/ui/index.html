<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTO Trade Compliance Monitor</title>
    <link rel="stylesheet" href="/static/styles.css">
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://unpkg.com/hyperscript.org@0.9.12"></script>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🌍</text></svg>">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="logo">
                    <span class="logo-icon">🌍</span>
                    <h1>WTO Compliance Monitor</h1>
                </div>
                <nav class="nav-tabs">
                    <button class="nav-tab active" data-target="dashboard">Dashboard</button>
                    <button class="nav-tab" data-target="regulations">Regulations</button>
                    <button class="nav-tab" data-target="changes">Changes</button>
                    <button class="nav-tab" data-target="crawling">Crawling</button>
                    <button class="nav-tab" data-target="subscriptions">Subscriptions</button>
                </nav>
                <div class="header-actions">
                    <button class="btn-secondary" id="refresh-data">
                        <span class="icon">🔄</span>
                        Refresh
                    </button>
                    <div class="connection-status" id="connection-status">
                        <span class="status-dot connected"></span>
                        Connected
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Dashboard Tab -->
            <section id="dashboard" class="tab-content active">
                <div class="dashboard-grid">
                    <!-- Stats Cards -->
                    <div class="stats-section">
                        <h2>System Overview</h2>
                        <div class="stats-grid" 
                             hx-get="/api/dashboard/stats" 
                             hx-trigger="load, every 30s"
                             hx-target="this"
                             hx-swap="innerHTML">
                            <div class="stat-card loading">
                                <div class="stat-icon">📊</div>
                                <div class="stat-content">
                                    <div class="stat-number">Loading...</div>
                                    <div class="stat-label">Statistics</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Activity -->
                    <div class="activity-section">
                        <h2>Recent Changes</h2>
                        <div class="activity-feed" 
                             hx-get="/api/changes?limit=5" 
                             hx-trigger="load, every 60s"
                             hx-target="this"
                             hx-swap="innerHTML">
                            <div class="activity-item loading">
                                <div class="activity-icon">⏳</div>
                                <div class="activity-content">
                                    <div class="activity-title">Loading recent changes...</div>
                                    <div class="activity-meta">Please wait</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Country Distribution -->
                    <div class="countries-section">
                        <h2>Coverage by Country</h2>
                        <div class="countries-grid" id="countries-grid">
                            <country-coverage-component></country-coverage-component>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Regulations Tab -->
            <section id="regulations" class="tab-content">
                <div class="content-header">
                    <h2>Trade Regulations</h2>
                    <div class="filters">
                        <select id="country-filter" class="filter-select">
                            <option value="">All Countries</option>
                            <option value="US">United States</option>
                            <option value="EU">European Union</option>
                            <option value="JP">Japan</option>
                            <option value="CN">China</option>
                            <option value="GB">United Kingdom</option>
                        </select>
                        <select id="category-filter" class="filter-select">
                            <option value="">All Categories</option>
                            <option value="tariff">Tariff</option>
                            <option value="technical_regulation">Technical Regulation</option>
                            <option value="sanitary_phytosanitary">SPS Measures</option>
                            <option value="services_regulation">Services</option>
                        </select>
                        <button class="btn-primary" 
                                hx-get="/api/regulations" 
                                hx-include="#country-filter, #category-filter"
                                hx-target="#regulations-list"
                                hx-swap="innerHTML">
                            Apply Filters
                        </button>
                    </div>
                </div>
                <div class="regulations-container">
                    <div id="regulations-list" 
                         hx-get="/api/regulations?limit=10" 
                         hx-trigger="load"
                         hx-target="this"
                         hx-swap="innerHTML">
                        <regulation-card-component></regulation-card-component>
                    </div>
                </div>
            </section>

            <!-- Changes Tab -->
            <section id="changes" class="tab-content">
                <div class="content-header">
                    <h2>Regulatory Changes</h2>
                    <div class="filters">
                        <select id="priority-filter" class="filter-select">
                            <option value="">All Priorities</option>
                            <option value="critical">Critical</option>
                            <option value="high">High</option>
                            <option value="medium">Medium</option>
                            <option value="low">Low</option>
                        </select>
                        <input type="date" id="since-filter" class="filter-input" placeholder="Since date">
                        <button class="btn-primary" 
                                hx-get="/api/changes" 
                                hx-include="#priority-filter, #since-filter"
                                hx-target="#changes-list"
                                hx-swap="innerHTML">
                            Apply Filters
                        </button>
                    </div>
                </div>
                <div class="changes-container">
                    <div id="changes-list" 
                         hx-get="/api/changes?limit=20" 
                         hx-trigger="load"
                         hx-target="this"
                         hx-swap="innerHTML">
                        <change-item-component></change-item-component>
                    </div>
                </div>
            </section>

            <!-- Crawling Tab -->
            <section id="crawling" class="tab-content">
                <div class="content-header">
                    <h2>Web Crawling & Monitoring</h2>
                    <div class="filters">
                        <button class="btn-primary" 
                                hx-post="/api/crawl/start-all"
                                hx-target="#crawl-status"
                                hx-swap="afterbegin">
                            🚀 Start All Crawls
                        </button>
                        <button class="btn-secondary" 
                                hx-get="/api/crawl/jobs"
                                hx-target="#crawl-jobs-list"
                                hx-swap="innerHTML">
                            🔄 Refresh Jobs
                        </button>
                    </div>
                </div>

                <!-- Crawl Status -->
                <div id="crawl-status" class="crawl-status"></div>

                <!-- Web Sources -->
                <div class="crawl-section">
                    <h3>Web Sources</h3>
                    <div class="crawl-sources-container">
                        <div id="web-sources-list" 
                             hx-get="/api/crawl/sources" 
                             hx-trigger="load"
                             hx-target="this"
                             hx-swap="innerHTML">
                            <div class="loading-placeholder">Loading web sources...</div>
                        </div>
                    </div>
                </div>

                <!-- Crawl Jobs -->
                <div class="crawl-section">
                    <h3>Recent Crawl Jobs</h3>
                    <div class="crawl-jobs-container">
                        <div id="crawl-jobs-list" 
                             hx-get="/api/crawl/jobs?limit=10" 
                             hx-trigger="load"
                             hx-target="this"
                             hx-swap="innerHTML">
                            <div class="loading-placeholder">Loading crawl jobs...</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Subscriptions Tab -->
            <section id="subscriptions" class="tab-content">
                <div class="content-header">
                    <h2>Notification Subscriptions</h2>
                    <button class="btn-primary" onclick="openSubscriptionModal()">
                        + Create Subscription
                    </button>
                </div>
                <div class="subscriptions-container">
                    <subscription-manager-component></subscription-manager-component>
                </div>
            </section>
        </main>

        <!-- Subscription Modal -->
        <div id="subscription-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Create Notification Subscription</h3>
                    <button class="modal-close" onclick="closeSubscriptionModal()">&times;</button>
                </div>
                <form id="subscription-form" 
                      hx-post="/api/subscriptions" 
                      hx-trigger="submit"
                      hx-target="#subscription-result"
                      hx-swap="innerHTML">
                    <div class="form-group">
                        <label for="sub-name">Subscription Name</label>
                        <input type="text" id="sub-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="sub-countries">Countries</label>
                        <select id="sub-countries" name="country_codes" multiple>
                            <option value="US">United States</option>
                            <option value="EU">European Union</option>
                            <option value="JP">Japan</option>
                            <option value="CN">China</option>
                            <option value="GB">United Kingdom</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sub-categories">Categories</label>
                        <select id="sub-categories" name="regulation_categories" multiple>
                            <option value="tariff">Tariff</option>
                            <option value="technical_regulation">Technical Regulation</option>
                            <option value="sanitary_phytosanitary">SPS Measures</option>
                            <option value="services_regulation">Services</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sub-priority">Priority Threshold</label>
                        <select id="sub-priority" name="priority_threshold">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                            <option value="critical">Critical</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="sub-webhook">Webhook URL</label>
                        <input type="url" id="sub-webhook" name="webhook_url" placeholder="https://your-app.com/webhook">
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-secondary" onclick="closeSubscriptionModal()">Cancel</button>
                        <button type="submit" class="btn-primary">Create Subscription</button>
                    </div>
                </form>
                <div id="subscription-result"></div>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- Load Web Components -->
    <script src="/static/components.js"></script>
    <script src="/static/app.js"></script>
</body>
</html>