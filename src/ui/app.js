// Main application JavaScript for WTO Compliance Monitor

// Global application state
const AppState = {
    currentTab: 'dashboard',
    subscriptions: [],
    connectionStatus: 'connected',
    lastUpdate: new Date()
};

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
    startPeriodicUpdates();
});

// Application initialization
function initializeApp() {
    console.log('🌍 WTO Compliance Monitor - Frontend Initialized');
    
    // Set initial tab
    showTab('dashboard');
    
    // Load initial data
    updateConnectionStatus();
    
    // Setup HTMX event listeners
    setupHTMXEvents();
}

// Event listeners setup
function setupEventListeners() {
    // Tab navigation
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            const target = this.dataset.target;
            showTab(target);
            setActiveTab(this);
        });
    });

    // Refresh button
    const refreshBtn = document.getElementById('refresh-data');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            refreshAllData();
            showToast('Data refreshed successfully', 'success');
        });
    }

    // Filter changes
    setupFilterListeners();
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
}

// Tab management
function showTab(tabName) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show selected tab
    const selectedTab = document.getElementById(tabName);
    if (selectedTab) {
        selectedTab.classList.add('active');
        AppState.currentTab = tabName;
    }
}

function setActiveTab(activeTabElement) {
    // Remove active class from all tabs
    document.querySelectorAll('.nav-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Add active class to clicked tab
    activeTabElement.classList.add('active');
}

// Filter functionality
function setupFilterListeners() {
    // Country filter for regulations
    const countryFilter = document.getElementById('country-filter');
    if (countryFilter) {
        countryFilter.addEventListener('change', function() {
            updateRegulationsList();
        });
    }

    // Category filter for regulations
    const categoryFilter = document.getElementById('category-filter');
    if (categoryFilter) {
        categoryFilter.addEventListener('change', function() {
            updateRegulationsList();
        });
    }

    // Priority filter for changes
    const priorityFilter = document.getElementById('priority-filter');
    if (priorityFilter) {
        priorityFilter.addEventListener('change', function() {
            updateChangesList();
        });
    }

    // Date filter for changes
    const sinceFilter = document.getElementById('since-filter');
    if (sinceFilter) {
        sinceFilter.addEventListener('change', function() {
            updateChangesList();
        });
    }
}

// Data update functions
function updateRegulationsList() {
    const countryFilter = document.getElementById('country-filter')?.value || '';
    const categoryFilter = document.getElementById('category-filter')?.value || '';
    
    const params = new URLSearchParams();
    if (countryFilter) params.append('country', countryFilter);
    if (categoryFilter) params.append('category', categoryFilter);
    params.append('limit', '10');
    
    const url = `/api/regulations?${params.toString()}`;
    
    htmx.ajax('GET', url, {
        target: '#regulations-list',
        swap: 'innerHTML'
    });
}

function updateChangesList() {
    const priorityFilter = document.getElementById('priority-filter')?.value || '';
    const sinceFilter = document.getElementById('since-filter')?.value || '';
    
    const params = new URLSearchParams();
    if (priorityFilter) params.append('priority', priorityFilter);
    if (sinceFilter) params.append('since', sinceFilter);
    params.append('limit', '20');
    
    const url = `/api/changes?${params.toString()}`;
    
    htmx.ajax('GET', url, {
        target: '#changes-list',
        swap: 'innerHTML'
    });
}

function refreshAllData() {
    // Add loading states
    addLoadingState();
    
    // Refresh dashboard stats
    htmx.ajax('GET', '/api/dashboard/stats', {
        target: '.stats-grid',
        swap: 'innerHTML'
    });
    
    // Refresh recent changes
    htmx.ajax('GET', '/api/changes?limit=5', {
        target: '.activity-feed',
        swap: 'innerHTML'
    });
    
    // Refresh current tab content
    if (AppState.currentTab === 'regulations') {
        updateRegulationsList();
    } else if (AppState.currentTab === 'changes') {
        updateChangesList();
    }
    
    // Remove loading states after delay
    setTimeout(removeLoadingState, 1000);
}

// Loading states
function addLoadingState() {
    document.querySelectorAll('.stat-card, .activity-feed, .regulation-card, .change-item')
        .forEach(element => {
            element.classList.add('loading');
        });
}

function removeLoadingState() {
    document.querySelectorAll('.loading').forEach(element => {
        element.classList.remove('loading');
    });
}

// Country filtering from dashboard
function filterByCountry(countryCode) {
    // Switch to regulations tab
    showTab('regulations');
    setActiveTab(document.querySelector('.nav-tab[data-target="regulations"]'));
    
    // Set country filter
    const countryFilter = document.getElementById('country-filter');
    if (countryFilter) {
        countryFilter.value = countryCode;
        updateRegulationsList();
    }
    
    showToast(`Showing regulations for ${countryCode}`, 'info');
}

// Subscription modal management
function openSubscriptionModal() {
    const modal = document.getElementById('subscription-modal');
    if (modal) {
        modal.classList.add('active');
        // Reset form
        const form = document.getElementById('subscription-form');
        if (form) {
            form.reset();
        }
    }
}

function closeSubscriptionModal() {
    const modal = document.getElementById('subscription-modal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// Subscription management
function editSubscription(subscriptionId) {
    console.log('Edit subscription:', subscriptionId);
    showToast('Edit functionality coming soon', 'info');
}

function deleteSubscription(subscriptionId) {
    if (confirm('Are you sure you want to delete this subscription?')) {
        // Remove from state
        AppState.subscriptions = AppState.subscriptions.filter(sub => sub.id !== subscriptionId);
        
        // Update UI
        const subscriptionManager = document.querySelector('subscription-manager-component');
        if (subscriptionManager) {
            subscriptionManager.removeSubscription(subscriptionId);
        }
        
        showToast('Subscription deleted successfully', 'success');
    }
}

// Toast notifications
function showToast(message, type = 'info', duration = 3000) {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return;
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.textContent = message;
    
    toastContainer.appendChild(toast);
    
    // Auto remove after duration
    setTimeout(() => {
        toast.style.animation = 'slideOutRight 0.3s ease-in-out';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}

// Connection status management
function updateConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    const statusDot = statusElement?.querySelector('.status-dot');
    
    if (!statusElement || !statusDot) return;
    
    // Test connection with a simple API call
    fetch('/api/dashboard/stats')
        .then(response => {
            if (response.ok) {
                AppState.connectionStatus = 'connected';
                statusDot.className = 'status-dot connected';
                statusElement.querySelector('span:last-child').textContent = 'Connected';
            } else {
                throw new Error('API request failed');
            }
        })
        .catch(() => {
            AppState.connectionStatus = 'disconnected';
            statusDot.className = 'status-dot disconnected';
            statusElement.querySelector('span:last-child').textContent = 'Disconnected';
            showToast('Connection lost - some features may not work', 'error');
        });
}

// Periodic updates
function startPeriodicUpdates() {
    // Update connection status every 30 seconds
    setInterval(updateConnectionStatus, 30000);
    
    // Update dashboard stats every 2 minutes when on dashboard tab
    setInterval(() => {
        if (AppState.currentTab === 'dashboard') {
            htmx.ajax('GET', '/api/dashboard/stats', {
                target: '.stats-grid',
                swap: 'innerHTML'
            });
        }
    }, 120000);
    
    // Update recent activity every minute when on dashboard tab
    setInterval(() => {
        if (AppState.currentTab === 'dashboard') {
            htmx.ajax('GET', '/api/changes?limit=5', {
                target: '.activity-feed',
                swap: 'innerHTML'
            });
        }
    }, 60000);
}

// Keyboard shortcuts
function handleKeyboardShortcuts(event) {
    // Only handle shortcuts when not in input fields
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA' || event.target.tagName === 'SELECT') {
        return;
    }
    
    switch (event.key) {
        case '1':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                showTab('dashboard');
                setActiveTab(document.querySelector('.nav-tab[data-target="dashboard"]'));
            }
            break;
        case '2':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                showTab('regulations');
                setActiveTab(document.querySelector('.nav-tab[data-target="regulations"]'));
            }
            break;
        case '3':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                showTab('changes');
                setActiveTab(document.querySelector('.nav-tab[data-target="changes"]'));
            }
            break;
        case '4':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                showTab('subscriptions');
                setActiveTab(document.querySelector('.nav-tab[data-target="subscriptions"]'));
            }
            break;
        case 'r':
            if (event.ctrlKey || event.metaKey) {
                event.preventDefault();
                refreshAllData();
                showToast('Data refreshed', 'success');
            }
            break;
        case 'Escape':
            closeSubscriptionModal();
            break;
    }
}

// HTMX event setup
function setupHTMXEvents() {
    // Handle successful responses
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful) {
            console.log('HTMX request successful:', event.detail.pathInfo.requestPath);
            AppState.lastUpdate = new Date();
        } else {
            console.error('HTMX request failed:', event.detail.pathInfo.requestPath);
            showToast('Failed to load data', 'error');
        }
    });
    
    // Handle loading states
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        const target = event.detail.target;
        if (target && target.classList) {
            target.classList.add('loading');
        }
    });
    
    document.body.addEventListener('htmx:afterRequest', function(event) {
        const target = event.detail.target;
        if (target && target.classList) {
            target.classList.remove('loading');
        }
    });
    
    // Handle form submissions
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 201 && event.detail.pathInfo.requestPath === '/api/subscriptions') {
            // Subscription created successfully
            closeSubscriptionModal();
            showToast('Subscription created successfully', 'success');
            
            // Add to local state and update UI
            try {
                const response = JSON.parse(event.detail.xhr.responseText);
                if (response.success) {
                    AppState.subscriptions.push(response.data);
                    const subscriptionManager = document.querySelector('subscription-manager-component');
                    if (subscriptionManager) {
                        subscriptionManager.addSubscription(response.data);
                    }
                }
            } catch (error) {
                console.error('Error parsing subscription response:', error);
            }
        }
    });
}

// Template processing for HTMX responses
function processStatsTemplate(data) {
    if (!data || !data.overview) return '';
    
    const { overview, distributions } = data;
    
    return `
        <div class="stat-card">
            <div class="stat-icon">📊</div>
            <div class="stat-content">
                <div class="stat-number">${overview.totalRegulations}</div>
                <div class="stat-label">Total Regulations</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">🔄</div>
            <div class="stat-content">
                <div class="stat-number">${overview.totalChanges}</div>
                <div class="stat-label">Total Changes</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">⚡</div>
            <div class="stat-content">
                <div class="stat-number">${overview.recentChanges}</div>
                <div class="stat-label">Recent Changes</div>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">🌍</div>
            <div class="stat-content">
                <div class="stat-number">${overview.countriesMonitored}</div>
                <div class="stat-label">Countries Monitored</div>
            </div>
        </div>
    `;
}

function processChangesTemplate(data) {
    if (!data || !data.changes || data.changes.length === 0) {
        return `
            <div class="activity-item">
                <div class="activity-icon">📋</div>
                <div class="activity-content">
                    <div class="activity-title">No recent changes</div>
                    <div class="activity-meta">All regulations are up to date</div>
                </div>
            </div>
        `;
    }
    
    return data.changes.map(change => {
        const priorityIcons = {
            critical: '🚨',
            high: '⚠️',
            medium: '📝',
            low: '📋'
        };
        
        return `
            <div class="activity-item">
                <div class="activity-icon">${priorityIcons[change.priority] || '📝'}</div>
                <div class="activity-content">
                    <div class="activity-title">${change.summary.en || 'Change detected'}</div>
                    <div class="activity-meta">
                        ${change.change_type.replace('_', ' ')} • 
                        ${new Date(change.detected_at).toLocaleDateString()}
                    </div>
                </div>
                <div class="activity-time">${new Date(change.detected_at).toLocaleTimeString()}</div>
            </div>
        `;
    }).join('');
}

// Utility functions
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString();
}

function formatPriority(priority) {
    return priority.charAt(0).toUpperCase() + priority.slice(1);
}

function formatCategory(category) {
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

// Export for global access
window.AppState = AppState;
window.showTab = showTab;
window.filterByCountry = filterByCountry;
window.openSubscriptionModal = openSubscriptionModal;
window.closeSubscriptionModal = closeSubscriptionModal;
window.editSubscription = editSubscription;
window.deleteSubscription = deleteSubscription;
window.showToast = showToast;
window.refreshAllData = refreshAllData;