/* WTO Compliance Monitor - Semantic CSS with Water.css Integration */

/*
 * This stylesheet complements Water.css by adding application-specific styles
 * while preserving Water.css's semantic HTML styling foundation.
 * We selectively override Water.css layout constraints that interfere with our app layout.
 */

/*
 * Critical Layout Overrides for Water.css Compatibility
 * Water.css applies max-width and centering that breaks full-width layouts
 */
html {
  margin: 0 !important;
  padding: 0 !important;
}

/* Override Water.css container constraints */
body,
.app-container,
header,
main,
section {
  max-width: none !important;
}

:root {
  /* Application-specific color palette that works with Water.css */
  --wto-primary: #2563eb;
  --wto-primary-hover: #1d4ed8;
  --wto-success: #059669;
  --wto-warning: #d97706;
  --wto-danger: #dc2626;
  --wto-info: #0891b2;

  /* Semantic spacing and effects */
  --wto-border-radius: 8px;
  --wto-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --wto-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --wto-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Animation and transition settings */
  --wto-transition: all 0.2s ease;
  --wto-transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/*
 * Override Water.css body styling for proper application layout
 * Water.css adds max-width and centering that breaks our layout
 */
body {
  overflow-x: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  max-width: none !important;
  /* Override Water.css max-width */
  margin: 0 !important;
  /* Override Water.css centering */
  padding: 0 !important;
  /* Override Water.css padding */
}

/*
 * Application Layout Components
 * These enhance Water.css's semantic styling with app-specific layout
 */

/* Main application container - override Water.css container behavior */
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: none !important;
  /* Override Water.css max-width */
  margin: 0 !important;
  /* Override Water.css centering */
  padding: 0 !important;
  /* Override Water.css padding */
}

/* Header styling that complements Water.css header elements */
header {
  background: linear-gradient(135deg, white 0%, #f8fafc 100%);
  border-bottom: 1px solid #e2e8f0;
  box-shadow: var(--wto-shadow-md);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  width: 100%;
  margin: 0 !important;
  /* Override Water.css margins */
  padding: 0 !important;
  /* Override Water.css padding */
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 64px;
  gap: 1rem;
}

/* Logo styling that works with Water.css h1 defaults */
.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  font-size: 2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.logo h1 {
  margin: 0;
  /* Override Water.css default margins for inline use */
  font-size: 1.5rem;
}

/*
 * Navigation Components
 * Enhanced navigation that works with Water.css button and nav styling
 */

/* Navigation container */
nav {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

/* Navigation tabs - enhance Water.css button styling */
.nav-tab {
  padding: 0.75rem 1.5rem;
  border: 1px solid transparent;
  background: transparent;
  cursor: pointer;
  border-radius: 25px;
  font-weight: 600;
  font-size: 0.875rem;
  transition: var(--wto-transition-slow);
  position: relative;
  overflow: hidden;
  color: inherit;
  /* Use Water.css color defaults */
}

.nav-tab::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(37, 99, 235, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-tab:hover::before {
  left: 100%;
}

.nav-tab:hover {
  background: rgba(37, 99, 235, 0.1);
  transform: translateY(-1px);
  box-shadow: var(--wto-shadow-sm);
  border-color: var(--wto-primary);
}

.nav-tab.active {
  background: var(--wto-primary);
  color: white;
  box-shadow: var(--wto-shadow-md);
  border-color: var(--wto-primary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--gray-600);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.connected {
  background: var(--success-color);
  animation: pulse 2s infinite;
}

.status-dot.disconnected {
  background: var(--danger-color);
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/*
 * Main Content Area
 * Override Water.css main element styling for proper layout
 */
main {
  flex: 1;
  max-width: 1400px;
  margin: 0 auto !important;
  /* Override Water.css margins */
  padding: 2rem 1.5rem !important;
  /* Override Water.css padding */
  width: 100%;
  box-sizing: border-box;
}

/*
 * Enhanced Button Styling
 * Work with Water.css button defaults while adding application-specific styles
 */

/* Primary action buttons - enhance Water.css button styling */
button:not(.nav-tab),
.btn-primary {
  background: var(--wto-primary);
  border-color: var(--wto-primary);
  color: white;
  transition: var(--wto-transition);
  border-radius: var(--wto-border-radius);
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
}

button:not(.nav-tab):hover,
.btn-primary:hover {
  background: var(--wto-primary-hover);
  border-color: var(--wto-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--wto-shadow-md);
}

/* Secondary buttons - enhance Water.css defaults */
.btn-secondary {
  background: white;
  border: 1px solid #e2e8f0;
  color: inherit;
  transition: var(--wto-transition);
  border-radius: var(--wto-border-radius);
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-secondary:hover {
  background: #f8fafc;
  border-color: var(--wto-primary);
  box-shadow: var(--wto-shadow-sm);
}

/*
 * Tab System
 * Semantic section-based tabs that work with Water.css
 */
.tab-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/*
 * Enhanced Form Elements
 * Complement Water.css form styling
 */
select,
input[type="date"],
.filter-select,
.filter-input {
  transition: var(--wto-transition);
  border-radius: var(--wto-border-radius);
}

select:focus,
input[type="date"]:focus,
.filter-select:focus,
.filter-input:focus {
  border-color: var(--wto-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

/*
 * Dashboard Layout
 * Semantic grid layout that works with Water.css section styling
 */
.dashboard-grid {
  display: grid;
  gap: 2rem;
  grid-template-columns: 1fr;
}

/* Enhance Water.css section and h2 styling for dashboard */
section h2 {
  margin-bottom: 1rem;
  color: var(--wto-primary);
  border-bottom: 2px solid var(--wto-primary);
  padding-bottom: 0.5rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

/*
 * Card Components
 * Semantic article-based cards that enhance Water.css
 */
article,
.stat-card,
.regulation-card,
.change-item {
  background: white;
  padding: 1.5rem;
  border-radius: var(--wto-border-radius);
  box-shadow: var(--wto-shadow-sm);
  border: 1px solid #e2e8f0;
  transition: var(--wto-transition-slow);
  position: relative;
  overflow: hidden;
}

article:hover,
.stat-card:hover,
.regulation-card:hover,
.change-item:hover {
  box-shadow: var(--wto-shadow-md);
  transform: translateY(-2px);
  border-color: var(--wto-primary);
}

/*
 * Stat Card Components
 * Enhance semantic content within cards
 */
.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2rem;
  padding: 0.75rem;
  border-radius: var(--wto-border-radius);
  background: rgba(37, 99, 235, 0.1);
  color: var(--wto-primary);
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.stat-label {
  font-size: 0.875rem;
  font-weight: 500;
  opacity: 0.8;
  margin: 0;
}

/*
 * Activity Feed and Lists
 * Semantic list styling that works with Water.css
 */
.activity-feed,
ul.activity-feed {
  background: white;
  border-radius: var(--wto-border-radius);
  box-shadow: var(--wto-shadow-sm);
  border: 1px solid #e2e8f0;
  overflow: hidden;
  list-style: none;
  padding: 0;
  margin: 0;
}

/* Activity items - can be li elements or divs */
.activity-item,
li.activity-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f5f9;
  transition: var(--wto-transition);
  list-style: none;
}

.activity-item:last-child,
li.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover,
li.activity-item:hover {
  background: #f8fafc;
}

/*
 * Status Indicators and Badges
 * Semantic status styling that works with Water.css
 */
.activity-icon,
.status-dot {
  font-size: 1.5rem;
  padding: 0.5rem;
  border-radius: var(--wto-border-radius);
  background: rgba(37, 99, 235, 0.1);
  color: var(--wto-primary);
  flex-shrink: 0;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  padding: 0;
  display: inline-block;
}

.status-dot.connected {
  background: var(--wto-success);
  animation: pulse 2s infinite;
}

.status-dot.disconnected {
  background: var(--wto-danger);
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.activity-meta {
  font-size: 0.875rem;
  opacity: 0.8;
}

.activity-time {
  font-size: 0.75rem;
  opacity: 0.6;
  white-space: nowrap;
}

/*
 * Content Headers and Filters
 * Semantic header styling that works with Water.css
 */
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.filters {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

/*
 * Responsive Design
 * Mobile-first approach that works with Water.css
 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
    padding: 1rem;
  }

  nav {
    justify-content: center;
  }

  .nav-tab {
    flex: 1;
    text-align: center;
    padding: 0.5rem;
    font-size: 0.8rem;
  }

  main {
    padding: 1rem !important;
    /* Override Water.css padding on mobile */
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .content-header {
    flex-direction: column;
    align-items: stretch;
  }

  .filters {
    justify-content: center;
  }
}

/* Additional layout fixes for Water.css compatibility */
* {
  box-sizing: border-box;
}

/* Ensure sections don't inherit Water.css container styles */
section {
  max-width: none !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/*
 * Enhanced Table Styling
 * Complement Water.css table defaults
 */
table {
  border-radius: var(--wto-border-radius);
  overflow: hidden;
  box-shadow: var(--wto-shadow-sm);
}

table th {
  background: var(--wto-primary);
  color: white;
  font-weight: 600;
}

table tr:hover {
  background: rgba(37, 99, 235, 0.05);
}

/*
 * Utility Classes
 * Essential utilities that work with Water.css
 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.gap-1 {
  gap: 0.5rem;
}

.gap-2 {
  gap: 1rem;
}

.mb-2 {
  margin-bottom: 1rem;
}

.mt-2 {
  margin-top: 1rem;
}

/*
 * Container Layouts
 * Semantic container styling
 */
.regulations-container,
.changes-container {
  display: grid;
  gap: 1.5rem;
}

/*
 * Connection Status
 * Status indicator styling that works with Water.css
 */
.connection-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

/*
 * Badge Components
 * Semantic badge styling
 */
.regulation-badge,
.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--wto-shadow-sm);
  transition: var(--wto-transition);
}

.regulation-badge::before,
.priority-badge::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.8;
}

.badge-country {
  background: var(--wto-info);
  color: white;
}

.badge-category {
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

.priority-critical {
  background: var(--wto-danger);
  color: white;
}

.priority-high {
  background: var(--wto-warning);
  color: white;
}

.priority-medium {
  background: var(--wto-info);
  color: white;
}

.priority-low {
  background: #f1f5f9;
  color: #64748b;
}

/* End of Water.css enhanced styles */

.regulation-card {
  background: linear-gradient(135deg, white 0%, #fafbfc 100%);
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), 0 8px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.regulation-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color), var(--info-color));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.regulation-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 16px 32px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.regulation-card:hover::after {
  opacity: 1;
}

.regulation-header {
  padding: 2rem 2rem 1rem 2rem;
  border-bottom: 1px solid var(--gray-100);
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.02) 0%, rgba(37, 99, 235, 0.01) 100%);
}

.regulation-title {
  font-size: 1.375rem;
  font-weight: 800;
  color: var(--gray-900);
  margin-bottom: 0.75rem;
  line-height: 1.3;
  background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.regulation-meta {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.regulation-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.375rem 1rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease;
}

.regulation-badge::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  opacity: 0.8;
}

.badge-country {
  background: linear-gradient(135deg, var(--info-color), #0e7490);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.badge-category {
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.regulation-badge:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.regulation-body {
  padding: 2rem;
}

.regulation-description {
  color: var(--gray-600);
  line-height: 1.7;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.regulation-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-100);
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: white;
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.detail-label {
  font-size: 0.7rem;
  font-weight: 700;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.detail-label::before {
  content: "•";
  color: var(--primary-color);
  font-weight: 900;
}

.detail-value {
  font-weight: 600;
  color: var(--gray-900);
  font-size: 0.9rem;
}

/* Change Items */
.changes-container {
  display: grid;
  gap: 1.5rem;
}

.change-item {
  background: linear-gradient(135deg, white 0%, #fafbfc 100%);
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), 0 8px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.change-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: var(--gray-300);
  transition: all 0.3s ease;
}

.change-item[data-priority="critical"]::before {
  background: linear-gradient(180deg, var(--danger-color), #b91c1c);
}

.change-item[data-priority="high"]::before {
  background: linear-gradient(180deg, var(--warning-color), #c2410c);
}

.change-item[data-priority="medium"]::before {
  background: linear-gradient(180deg, var(--info-color), #0e7490);
}

.change-item[data-priority="low"]::before {
  background: linear-gradient(180deg, var(--gray-400), var(--gray-500));
}

.change-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 16px 32px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.change-item:hover::before {
  width: 6px;
}

.change-header {
  padding: 2rem 2rem 1rem 2rem;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1rem;
  border-bottom: 1px solid var(--gray-100);
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.01) 0%, transparent 100%);
}

.change-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-900);
  line-height: 1.4;
  flex: 1;
  background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.priority-badge::before {
  content: "";
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  box-shadow: 0 0 8px currentColor;
  animation: priorityPulse 2s infinite;
}

@keyframes priorityPulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.priority-critical {
  background: linear-gradient(135deg, var(--danger-color), #b91c1c);
  color: white;
}

.priority-high {
  background: linear-gradient(135deg, var(--warning-color), #c2410c);
  color: white;
}

.priority-medium {
  background: linear-gradient(135deg, var(--info-color), #0e7490);
  color: white;
}

.priority-low {
  background: linear-gradient(135deg, var(--gray-300), var(--gray-400));
  color: var(--gray-700);
}

.priority-badge:hover {
  transform: translateY(-1px) scale(1.05);
  box-shadow: var(--shadow-md);
}

.change-body {
  padding: 2rem;
}

.change-summary {
  color: var(--gray-600);
  margin-bottom: 1.5rem;
  line-height: 1.7;
  font-size: 0.95rem;
}

.change-meta {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: var(--gray-500);
  flex-wrap: wrap;
}

.change-meta>span {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.change-meta>span:first-child {
  font-weight: 600;
  color: var(--gray-700);
}

/* Modal */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  animation: fadeIn 0.3s ease-in-out;
}

.modal.active {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-900);
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--gray-500);
  padding: 0.25rem;
  border-radius: var(--border-radius);
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

/* Forms */
form {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: white;
  color: var(--gray-900);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-group select[multiple] {
  height: 120px;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid var(--gray-200);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  top: 1rem;
  right: 1rem;
  z-index: 1100;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.toast {
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-lg);
  color: white;
  font-weight: 500;
  max-width: 350px;
  animation: slideInRight 0.3s ease-in-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.toast.success {
  background: var(--success-color);
}

.toast.error {
  background: var(--danger-color);
}

.toast.info {
  background: var(--info-color);
}

.toast.warning {
  background: var(--warning-color);
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--gray-300);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 2fr 1fr;
    grid-template-areas:
      "stats activity"
      "countries countries";
  }

  .stats-section {
    grid-area: stats;
  }

  .activity-section {
    grid-area: activity;
  }

  .countries-section {
    grid-area: countries;
  }
}

@media (min-width: 1200px) {
  .regulations-container {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  }

  .changes-container {
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
  }

  .nav-tabs {
    width: 100%;
    justify-content: center;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .nav-tabs::-webkit-scrollbar {
    display: none;
  }

  .nav-tab {
    white-space: nowrap;
  }

  .content-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .filters {
    width: 100%;
    gap: 0.75rem;
  }

  .filter-select,
  .filter-input {
    flex: 1;
    min-width: 0;
  }

  .regulation-header,
  .regulation-body,
  .change-header,
  .change-body {
    padding: 1.5rem;
  }

  .regulation-title,
  .change-title {
    font-size: 1.125rem;
  }

  .regulation-details {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .stat-icon {
    align-self: center;
  }
}

@media (max-width: 480px) {
  .app-main {
    padding: 1rem;
  }

  .regulation-card,
  .change-item {
    border-radius: 8px;
  }

  .regulation-header,
  .regulation-body,
  .change-header,
  .change-body {
    padding: 1rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 1rem;
  }

  .regulation-meta,
  .change-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* Dark mode support (system preference) */
@media (prefers-color-scheme: dark) {
  :root {
    --gray-50: #1f2937;
    --gray-100: #374151;
    --gray-200: #4b5563;
    --gray-300: #6b7280;
    --gray-400: #9ca3af;
    --gray-500: #d1d5db;
    --gray-600: #e5e7eb;
    --gray-700: #f3f4f6;
    --gray-800: #f9fafb;
    --gray-900: #ffffff;
  }

  body {
    background-color: var(--gray-50);
    color: var(--gray-900);
  }

  .regulation-card,
  .change-item,
  .stat-card,
  .activity-feed,
  .empty-state {
    background: linear-gradient(135deg, var(--gray-100) 0%, #2d3748 100%);
    border-color: var(--gray-200);
  }

  .app-header {
    background: var(--gray-100);
    border-color: var(--gray-200);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {

  .regulation-card,
  .change-item,
  .stat-card {
    border-width: 2px;
    border-color: var(--gray-600);
  }

  .btn-primary,
  .btn-secondary {
    border-width: 2px;
  }

  .priority-badge {
    border-width: 2px;
    border-color: rgba(0, 0, 0, 0.3);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {

  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .loading-overlay::before,
  .priority-badge::before {
    animation: none;
  }
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: 3rem 2rem;
  color: var(--gray-500);
  background: white;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.6;
}

/* Impact Progress Bars */
.impact-bar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.impact-progress {
  flex: 1;
  height: 8px;
  background: var(--gray-200);
  border-radius: 4px;
  overflow: hidden;
}

.impact-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--danger-color));
  border-radius: 4px;
  transition: width 0.3s ease;
}

.impact-bar span {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--gray-600);
  min-width: 30px;
}

/* Industry Tags */
.change-industries {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.industry-tag {
  display: inline-block;
  padding: 0.25rem 0.75rem;
  background: var(--gray-100);
  color: var(--gray-700);
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  border: 1px solid var(--gray-200);
}

/* Enhanced Loading States */
.loading-overlay {
  position: relative;
  overflow: hidden;
}

.loading-overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.6),
      transparent);
  animation: shimmer 1.5s infinite;
  z-index: 1;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.skeleton {
  background: var(--gray-200);
  border-radius: var(--border-radius);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.skeleton-text {
  height: 1rem;
  background: var(--gray-200);
  border-radius: 4px;
  margin-bottom: 0.5rem;
}

.skeleton-text.short {
  width: 60%;
}

.skeleton-text.medium {
  width: 80%;
}

.skeleton-text.long {
  width: 100%;
}

/* Enhanced Card Hover Effects */
.regulation-card,
.change-item,
.stat-card {
  position: relative;
  overflow: hidden;
}

.regulation-card::before,
.change-item::before,
.stat-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(37, 99, 235, 0.03),
      transparent);
  transition: left 0.5s ease;
  z-index: 0;
}

.regulation-card:hover::before,
.change-item:hover::before,
.stat-card:hover::before {
  left: 100%;
}

.regulation-card>*,
.change-item>*,
.stat-card>* {
  position: relative;
  z-index: 1;
}

/* Improved Priority Badges */
.priority-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  box-shadow: var(--shadow-sm);
}

.priority-badge::before {
  content: "";
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

/* Enhanced Stat Cards */
.stat-card {
  position: relative;
  background: linear-gradient(135deg, white 0%, var(--gray-50) 100%);
  border: 1px solid var(--gray-200);
}

.stat-card .stat-icon {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
  color: white;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  width: 3rem;
  height: 3rem;
  box-shadow: var(--shadow-sm);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--gray-900), var(--gray-700));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
}

/* Enhanced Activity Feed */
.activity-item {
  position: relative;
  background: linear-gradient(135deg, white 0%, var(--gray-50) 100%);
}

.activity-item::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: var(--primary-color);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.activity-item:hover::after {
  opacity: 1;
}

.activity-icon {
  background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
  border: 2px solid white;
  box-shadow: var(--shadow-sm);
}

/* Better Form Styling */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus,
.filter-select:focus,
.filter-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
  outline: none;
}

.form-group input,
.form-group select,
.form-group textarea,
.filter-select,
.filter-input {
  transition: all 0.2s ease;
}

/* Enhanced Buttons */
.btn-primary,
.btn-secondary {
  position: relative;
  overflow: hidden;
  font-weight: 600;
  letter-spacing: 0.025em;
}

.btn-primary::before,
.btn-secondary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent);
  transition: left 0.5s ease;
}

.btn-primary:hover::before,
.btn-secondary:hover::before {
  left: 100%;
}

/* Toast Improvements */
.toast {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.toast.success {
  background: linear-gradient(135deg, var(--success-color), #047857);
}

.toast.error {
  background: linear-gradient(135deg, var(--danger-color), #b91c1c);
}

.toast.info {
  background: linear-gradient(135deg, var(--info-color), #0e7490);
}

.toast.warning {
  background: linear-gradient(135deg, var(--warning-color), #c2410c);
}

/* Crawling Interface */
.crawl-section {
  margin-bottom: 2rem;
}

.crawl-section h3 {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.crawl-section h3::before {
  content: "📊";
  font-size: 1rem;
}

.crawl-status {
  margin-bottom: 2rem;
}

.crawl-status .toast {
  position: relative;
  margin-bottom: 0.5rem;
}

.crawl-sources-container,
.crawl-jobs-container {
  display: grid;
  gap: 1rem;
}

.web-source-card {
  background: linear-gradient(135deg, white 0%, #fafbfc 100%);
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.04), 0 8px 16px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.web-source-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 16px 32px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

.web-source-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.web-source-info h4 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: 0.25rem;
}

.web-source-url {
  font-size: 0.875rem;
  color: var(--info-color);
  font-family: var(--font-mono);
}

.web-source-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--success-color);
}

.status-indicator.inactive {
  background: var(--gray-400);
}

.web-source-meta {
  padding: 1.5rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.meta-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.meta-value {
  font-size: 0.875rem;
  color: var(--gray-900);
  font-weight: 500;
}

.crawl-job-card {
  background: linear-gradient(135deg, white 0%, #fafbfc 100%);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.crawl-job-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.crawl-job-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.job-id {
  font-family: var(--font-mono);
  font-size: 0.875rem;
  color: var(--gray-600);
}

.job-status {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.job-status.running {
  background: var(--info-color);
  color: white;
}

.job-status.completed {
  background: var(--success-color);
  color: white;
}

.job-status.failed {
  background: var(--danger-color);
  color: white;
}

.job-status.pending {
  background: var(--warning-color);
  color: white;
}

.job-metrics {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
}

.job-metric {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.job-metric-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--primary-color);
}

.job-metric-label {
  font-size: 0.75rem;
  color: var(--gray-500);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.loading-placeholder {
  text-align: center;
  padding: 2rem;
  color: var(--gray-500);
  font-style: italic;
}

.crawl-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.crawl-actions .btn-secondary {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.font-mono {
  font-family: var(--font-mono);
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}