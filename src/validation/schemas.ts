// Validation schemas using pure TypeScript functions
import { match } from "ts-pattern";
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";

// Validation error type
export type ValidationError = {
  readonly field: string;
  readonly message: string;
  readonly code: string;
};

// Create validation error
const createValidationError = (field: string, message: string, code = "VALIDATION_ERROR"): ValidationError => ({
  field,
  message,
  code,
});

// Pure validation functions
export const validateCountryCode = (value: unknown): Result<string, ValidationError> => {
  if (typeof value !== "string") {
    return Err(createValidationError("country_code", "Must be a string"));
  }
  if (value.length < 2 || value.length > 3) {
    return Err(createValidationError("country_code", "Must be 2-3 characters long"));
  }
  if (!/^[A-Z]{2,3}$/.test(value)) {
    return Err(createValidationError("country_code", "Must be a valid ISO country code"));
  }
  return Ok(value);
};

export const validateLanguageCode = (value: unknown): Result<string, ValidationError> => {
  if (typeof value !== "string") {
    return Err(createValidationError("language_code", "Must be a string"));
  }
  if (value.length !== 2) {
    return Err(createValidationError("language_code", "Must be exactly 2 characters"));
  }
  if (!/^[a-z]{2}$/.test(value)) {
    return Err(createValidationError("language_code", "Must be a valid ISO language code"));
  }
  return Ok(value);
};

export const validateHSCode = (value: unknown): Result<string, ValidationError> => {
  if (typeof value !== "string") {
    return Err(createValidationError("hs_code", "Must be a string"));
  }
  if (!/^\d{4,10}$/.test(value)) {
    return Err(createValidationError("hs_code", "Must be a valid HS code (4-10 digits)"));
  }
  return Ok(value);
};

const PRIORITIES = ["critical", "high", "medium", "low"] as const;
export type Priority = typeof PRIORITIES[number];

export const validatePriority = (value: unknown): Result<Priority, ValidationError> => {
  if (typeof value !== "string") {
    return Err(createValidationError("priority", "Must be a string"));
  }
  if (!PRIORITIES.includes(value as Priority)) {
    return Err(createValidationError("priority", "Must be one of: critical, high, medium, low"));
  }
  return Ok(value as Priority);
};

const REGULATION_CATEGORIES = [
  "tariff",
  "non_tariff_barrier", 
  "technical_regulation",
  "sanitary_phytosanitary",
  "trade_remedies",
  "services_regulation",
  "intellectual_property",
  "government_procurement",
  "customs_procedure",
  "trade_facilitation"
] as const;
export type RegulationCategory = typeof REGULATION_CATEGORIES[number];

export const validateRegulationCategory = (value: unknown): Result<RegulationCategory, ValidationError> => {
  if (typeof value !== "string") {
    return Err(createValidationError("category", "Must be a string"));
  }
  if (!REGULATION_CATEGORIES.includes(value as RegulationCategory)) {
    return Err(createValidationError("category", "Must be a valid regulation category"));
  }
  return Ok(value as RegulationCategory);
};

const CHANGE_TYPES = [
  "new_regulation",
  "amendment", 
  "repeal",
  "effective_date_change",
  "scope_modification",
  "rate_adjustment"
] as const;
export type ChangeType = typeof CHANGE_TYPES[number];

export const validateChangeType = (value: unknown): Result<ChangeType, ValidationError> => {
  if (typeof value !== "string") {
    return Err(createValidationError("change_type", "Must be a string"));
  }
  if (!CHANGE_TYPES.includes(value as ChangeType)) {
    return Err(createValidationError("change_type", "Must be a valid change type"));
  }
  return Ok(value as ChangeType);
};

// Complex validation functions
export const validateImpactScore = (value: unknown): Result<{ economic: number; operational: number; compliance: number; urgency: number }, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("impact_score", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  const fields = ["economic", "operational", "compliance", "urgency"];
  
  for (const field of fields) {
    if (typeof obj[field] !== "number") {
      return Err(createValidationError(field, "Must be a number"));
    }
    if (obj[field] < 0 || obj[field] > 10) {
      return Err(createValidationError(field, "Must be between 0 and 10"));
    }
  }
  
  return Ok({
    economic: obj["economic"] as number,
    operational: obj.operational as number,
    compliance: obj.compliance as number,
    urgency: obj.urgency as number,
  });
};

export const validateTimeline = (value: unknown): Result<{ effective_date: Date; implementation_deadline?: Date; consultation_period?: { start: Date; end: Date } }, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("timeline", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (!obj.effective_date) {
    return Err(createValidationError("effective_date", "Required field"));
  }
  
  const effectiveDate = new Date(obj.effective_date as string);
  if (isNaN(effectiveDate.getTime())) {
    return Err(createValidationError("effective_date", "Must be a valid date"));
  }
  
  const result: { effective_date: Date; implementation_deadline?: Date; consultation_period?: { start: Date; end: Date } } = {
    effective_date: effectiveDate,
  };
  
  if (obj.implementation_deadline) {
    const implDate = new Date(obj.implementation_deadline as string);
    if (isNaN(implDate.getTime())) {
      return Err(createValidationError("implementation_deadline", "Must be a valid date"));
    }
    result.implementation_deadline = implDate;
  }
  
  if (obj.consultation_period && typeof obj.consultation_period === "object") {
    const cp = obj.consultation_period as Record<string, unknown>;
    if (!cp.start || !cp.end) {
      return Err(createValidationError("consultation_period", "Must have start and end dates"));
    }
    const start = new Date(cp.start as string);
    const end = new Date(cp.end as string);
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return Err(createValidationError("consultation_period", "Start and end must be valid dates"));
    }
    result.consultation_period = { start, end };
  }
  
  return Ok(result);
};

export const validateMultiLangText = (value: unknown): Result<Record<string, string>, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("multi_lang_text", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  for (const [key, val] of Object.entries(obj)) {
    if (typeof val !== "string") {
      return Err(createValidationError(key, "Must be a string"));
    }
  }
  
  return Ok(obj as Record<string, string>);
};

export const validateDocumentMetadata = (value: unknown): Result<{ source_url: string; document_hash: string; content_type: string; file_size: number; extracted_at: Date; last_modified?: Date }, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("document_metadata", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.source_url !== "string" || !obj.source_url.startsWith("http")) {
    return Err(createValidationError("source_url", "Must be a valid URL"));
  }
  
  if (typeof obj.document_hash !== "string" || obj.document_hash.length === 0) {
    return Err(createValidationError("document_hash", "Must be a non-empty string"));
  }
  
  if (typeof obj.content_type !== "string" || obj.content_type.length === 0) {
    return Err(createValidationError("content_type", "Must be a non-empty string"));
  }
  
  if (typeof obj.file_size !== "number" || obj.file_size <= 0) {
    return Err(createValidationError("file_size", "Must be a positive number"));
  }
  
  const extractedAt = new Date(obj.extracted_at as string);
  if (isNaN(extractedAt.getTime())) {
    return Err(createValidationError("extracted_at", "Must be a valid date"));
  }
  
  const result: { source_url: string; document_hash: string; content_type: string; file_size: number; extracted_at: Date; last_modified?: Date } = {
    source_url: obj.source_url,
    document_hash: obj.document_hash,
    content_type: obj.content_type,
    file_size: obj.file_size,
    extracted_at: extractedAt,
  };
  
  if (obj.last_modified) {
    const lastModified = new Date(obj.last_modified as string);
    if (isNaN(lastModified.getTime())) {
      return Err(createValidationError("last_modified", "Must be a valid date"));
    }
    result.last_modified = lastModified;
  }
  
  return Ok(result);
};

// Regulation validation function
export const validateRegulation = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("regulation", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  // Validate required fields
  if (typeof obj.id !== "string" || !obj.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
    return Err(createValidationError("id", "Must be a valid UUID"));
  }
  
  const countryResult = validateCountryCode(obj.country_code);
  if (!countryResult.success) {
    return Err(countryResult.error);
  }
  
  if (typeof obj.source_agency !== "string" || obj.source_agency.length === 0 || obj.source_agency.length > 200) {
    return Err(createValidationError("source_agency", "Must be a string between 1 and 200 characters"));
  }
  
  const titleResult = validateMultiLangText(obj.title);
  if (!titleResult.success) {
    return Err(createValidationError("title", titleResult.error.message));
  }
  
  const descriptionResult = validateMultiLangText(obj.description);
  if (!descriptionResult.success) {
    return Err(createValidationError("description", descriptionResult.error.message));
  }
  
  const categoryResult = validateRegulationCategory(obj.category);
  if (!categoryResult.success) {
    return Err(categoryResult.error);
  }
  
  if (typeof obj.subcategory !== "string" || obj.subcategory.length === 0 || obj.subcategory.length > 100) {
    return Err(createValidationError("subcategory", "Must be a string between 1 and 100 characters"));
  }
  
  if (!Array.isArray(obj.hs_codes)) {
    return Err(createValidationError("hs_codes", "Must be an array"));
  }
  
  for (const hsCode of obj.hs_codes) {
    const hsResult = validateHSCode(hsCode);
    if (!hsResult.success) {
      return Err(hsResult.error);
    }
  }
  
  const timelineResult = validateTimeline(obj.timeline);
  if (!timelineResult.success) {
    return Err(timelineResult.error);
  }
  
  const impactResult = validateImpactScore(obj.impact_assessment);
  if (!impactResult.success) {
    return Err(impactResult.error);
  }
  
  if (!Array.isArray(obj.related_regulations)) {
    return Err(createValidationError("related_regulations", "Must be an array"));
  }
  
  const langResult = validateLanguageCode(obj.original_language);
  if (!langResult.success) {
    return Err(langResult.error);
  }
  
  const metadataResult = validateDocumentMetadata(obj.document_metadata);
  if (!metadataResult.success) {
    return Err(metadataResult.error);
  }
  
  if (typeof obj.version !== "number" || obj.version <= 0) {
    return Err(createValidationError("version", "Must be a positive number"));
  }
  
  const createdAt = new Date(obj.created_at as string);
  if (isNaN(createdAt.getTime())) {
    return Err(createValidationError("created_at", "Must be a valid date"));
  }
  
  const updatedAt = new Date(obj.updated_at as string);
  if (isNaN(updatedAt.getTime())) {
    return Err(createValidationError("updated_at", "Must be a valid date"));
  }
  
  return Ok(obj);
};

export const validateRegulationInput = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("regulation_input", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  // Similar validation but without id, version, created_at, updated_at
  const countryResult = validateCountryCode(obj.country_code);
  if (!countryResult.success) {
    return Err(countryResult.error);
  }
  
  if (typeof obj.source_agency !== "string" || obj.source_agency.length === 0 || obj.source_agency.length > 200) {
    return Err(createValidationError("source_agency", "Must be a string between 1 and 200 characters"));
  }
  
  const titleResult = validateMultiLangText(obj.title);
  if (!titleResult.success) {
    return Err(createValidationError("title", titleResult.error.message));
  }
  
  const descriptionResult = validateMultiLangText(obj.description);
  if (!descriptionResult.success) {
    return Err(createValidationError("description", descriptionResult.error.message));
  }
  
  return Ok(obj);
};

// Change validation functions
export const validateChangeDetail = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("change_detail", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.field !== "string" || obj.field.length === 0) {
    return Err(createValidationError("field", "Must be a non-empty string"));
  }
  
  if (typeof obj.change_description !== "string" || obj.change_description.length === 0) {
    return Err(createValidationError("change_description", "Must be a non-empty string"));
  }
  
  if (typeof obj.confidence_score !== "number" || obj.confidence_score < 0 || obj.confidence_score > 1) {
    return Err(createValidationError("confidence_score", "Must be a number between 0 and 1"));
  }
  
  return Ok(obj);
};

export const validateChange = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("change", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.id !== "string" || !obj.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
    return Err(createValidationError("id", "Must be a valid UUID"));
  }
  
  if (typeof obj.regulation_id !== "string" || !obj.regulation_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
    return Err(createValidationError("regulation_id", "Must be a valid UUID"));
  }
  
  const changeTypeResult = validateChangeType(obj.change_type);
  if (!changeTypeResult.success) {
    return Err(changeTypeResult.error);
  }
  
  const priorityResult = validatePriority(obj.priority);
  if (!priorityResult.success) {
    return Err(priorityResult.error);
  }
  
  const impactResult = validateImpactScore(obj.impact_score);
  if (!impactResult.success) {
    return Err(impactResult.error);
  }
  
  const summaryResult = validateMultiLangText(obj.summary);
  if (!summaryResult.success) {
    return Err(createValidationError("summary", summaryResult.error.message));
  }
  
  if (!Array.isArray(obj.detailed_changes)) {
    return Err(createValidationError("detailed_changes", "Must be an array"));
  }
  
  for (const detail of obj.detailed_changes) {
    const detailResult = validateChangeDetail(detail);
    if (!detailResult.success) {
      return Err(detailResult.error);
    }
  }
  
  if (!Array.isArray(obj.affected_industries)) {
    return Err(createValidationError("affected_industries", "Must be an array"));
  }
  
  const implDate = new Date(obj.implementation_date as string);
  if (isNaN(implDate.getTime())) {
    return Err(createValidationError("implementation_date", "Must be a valid date"));
  }
  
  if (!Array.isArray(obj.stakeholders)) {
    return Err(createValidationError("stakeholders", "Must be an array"));
  }
  
  const detectedAt = new Date(obj.detected_at as string);
  if (isNaN(detectedAt.getTime())) {
    return Err(createValidationError("detected_at", "Must be a valid date"));
  }
  
  if (typeof obj.notification_sent !== "boolean") {
    return Err(createValidationError("notification_sent", "Must be a boolean"));
  }
  
  return Ok(obj);
};

// Notification configuration validation functions
export const validateNotificationChannel = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("notification_channel", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.type !== "string") {
    return Err(createValidationError("type", "Must be a string"));
  }
  
  switch (obj.type) {
    case "email":
      if (!Array.isArray(obj.addresses)) {
        return Err(createValidationError("addresses", "Must be an array"));
      }
      for (const addr of obj.addresses) {
        if (typeof addr !== "string" || !addr.includes("@")) {
          return Err(createValidationError("addresses", "Must contain valid email addresses"));
        }
      }
      break;
    case "webhook":
      if (typeof obj.url !== "string" || !obj.url.startsWith("http")) {
        return Err(createValidationError("url", "Must be a valid URL"));
      }
      break;
    case "sms":
      if (!Array.isArray(obj.numbers)) {
        return Err(createValidationError("numbers", "Must be an array"));
      }
      for (const num of obj.numbers) {
        if (typeof num !== "string" || !num.match(/^\+?[1-9]\d{1,14}$/)) {
          return Err(createValidationError("numbers", "Must contain valid phone numbers"));
        }
      }
      break;
    case "slack":
      if (typeof obj.webhook_url !== "string" || !obj.webhook_url.startsWith("http")) {
        return Err(createValidationError("webhook_url", "Must be a valid URL"));
      }
      if (typeof obj.channel !== "string" || obj.channel.length === 0) {
        return Err(createValidationError("channel", "Must be a non-empty string"));
      }
      break;
    default:
      return Err(createValidationError("type", "Must be one of: email, webhook, sms, slack"));
  }
  
  return Ok(obj);
};

export const validateNotificationFilter = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("notification_filter", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.field !== "string") {
    return Err(createValidationError("field", "Must be a string"));
  }
  
  const validOperators = ["equals", "contains", "greater_than", "less_than"];
  if (typeof obj.operator !== "string" || !validOperators.includes(obj.operator)) {
    return Err(createValidationError("operator", "Must be one of: equals, contains, greater_than, less_than"));
  }
  
  return Ok(obj);
};

export const validateNotificationConfig = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("notification_config", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.enabled !== "boolean") {
    return Err(createValidationError("enabled", "Must be a boolean"));
  }
  
  const priorityResult = validatePriority(obj.priority_threshold);
  if (!priorityResult.success) {
    return Err(createValidationError("priority_threshold", priorityResult.error.message));
  }
  
  if (!Array.isArray(obj.channels)) {
    return Err(createValidationError("channels", "Must be an array"));
  }
  
  for (const channel of obj.channels) {
    const channelResult = validateNotificationChannel(channel);
    if (!channelResult.success) {
      return Err(channelResult.error);
    }
  }
  
  if (typeof obj.delay_minutes !== "number" || obj.delay_minutes < 0) {
    return Err(createValidationError("delay_minutes", "Must be a non-negative number"));
  }
  
  if (typeof obj.batch_notifications !== "boolean") {
    return Err(createValidationError("batch_notifications", "Must be a boolean"));
  }
  
  if (!Array.isArray(obj.custom_filters)) {
    return Err(createValidationError("custom_filters", "Must be an array"));
  }
  
  for (const filter of obj.custom_filters) {
    const filterResult = validateNotificationFilter(filter);
    if (!filterResult.success) {
      return Err(filterResult.error);
    }
  }
  
  return Ok(obj);
};

// Subscription validation functions
export const validateSubscription = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("subscription", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.id !== "string" || !obj.id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
    return Err(createValidationError("id", "Must be a valid UUID"));
  }
  
  if (typeof obj.user_id !== "string" || obj.user_id.length === 0) {
    return Err(createValidationError("user_id", "Must be a non-empty string"));
  }
  
  if (typeof obj.name !== "string" || obj.name.length === 0 || obj.name.length > 200) {
    return Err(createValidationError("name", "Must be a string between 1 and 200 characters"));
  }
  
  if (!Array.isArray(obj.country_codes)) {
    return Err(createValidationError("country_codes", "Must be an array"));
  }
  
  for (const cc of obj.country_codes) {
    const ccResult = validateCountryCode(cc);
    if (!ccResult.success) {
      return Err(ccResult.error);
    }
  }
  
  if (!Array.isArray(obj.regulation_categories)) {
    return Err(createValidationError("regulation_categories", "Must be an array"));
  }
  
  for (const cat of obj.regulation_categories) {
    const catResult = validateRegulationCategory(cat);
    if (!catResult.success) {
      return Err(catResult.error);
    }
  }
  
  if (!Array.isArray(obj.hs_codes)) {
    return Err(createValidationError("hs_codes", "Must be an array"));
  }
  
  for (const hs of obj.hs_codes) {
    const hsResult = validateHSCode(hs);
    if (!hsResult.success) {
      return Err(hsResult.error);
    }
  }
  
  const priorityResult = validatePriority(obj.priority_threshold);
  if (!priorityResult.success) {
    return Err(priorityResult.error);
  }
  
  const configResult = validateNotificationConfig(obj.notification_config);
  if (!configResult.success) {
    return Err(configResult.error);
  }
  
  const createdAt = new Date(obj.created_at as string);
  if (isNaN(createdAt.getTime())) {
    return Err(createValidationError("created_at", "Must be a valid date"));
  }
  
  const updatedAt = new Date(obj.updated_at as string);
  if (isNaN(updatedAt.getTime())) {
    return Err(createValidationError("updated_at", "Must be a valid date"));
  }
  
  if (typeof obj.active !== "boolean") {
    return Err(createValidationError("active", "Must be a boolean"));
  }
  
  return Ok(obj);
};

export const validateSubscriptionInput = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("subscription_input", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  
  if (typeof obj.user_id !== "string" || obj.user_id.length === 0) {
    return Err(createValidationError("user_id", "Must be a non-empty string"));
  }
  
  if (typeof obj.name !== "string" || obj.name.length === 0 || obj.name.length > 200) {
    return Err(createValidationError("name", "Must be a string between 1 and 200 characters"));
  }
  
  // Additional validation as needed
  return Ok(obj);
};

// API request validation functions
export const validateRegulationFilters = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("regulation_filters", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  const result: Record<string, unknown> = {};
  
  if (obj.country_codes !== undefined) {
    if (!Array.isArray(obj.country_codes)) {
      return Err(createValidationError("country_codes", "Must be an array"));
    }
    for (const cc of obj.country_codes) {
      const ccResult = validateCountryCode(cc);
      if (!ccResult.success) {
        return Err(ccResult.error);
      }
    }
    result.country_codes = obj.country_codes;
  }
  
  if (obj.categories !== undefined) {
    if (!Array.isArray(obj.categories)) {
      return Err(createValidationError("categories", "Must be an array"));
    }
    for (const cat of obj.categories) {
      const catResult = validateRegulationCategory(cat);
      if (!catResult.success) {
        return Err(catResult.error);
      }
    }
    result.categories = obj.categories;
  }
  
  if (obj.hs_codes !== undefined) {
    if (!Array.isArray(obj.hs_codes)) {
      return Err(createValidationError("hs_codes", "Must be an array"));
    }
    for (const hs of obj.hs_codes) {
      const hsResult = validateHSCode(hs);
      if (!hsResult.success) {
        return Err(hsResult.error);
      }
    }
    result.hs_codes = obj.hs_codes;
  }
  
  if (obj.effective_after !== undefined) {
    const date = new Date(obj.effective_after as string);
    if (isNaN(date.getTime())) {
      return Err(createValidationError("effective_after", "Must be a valid date"));
    }
    result.effective_after = date;
  }
  
  if (obj.effective_before !== undefined) {
    const date = new Date(obj.effective_before as string);
    if (isNaN(date.getTime())) {
      return Err(createValidationError("effective_before", "Must be a valid date"));
    }
    result.effective_before = date;
  }
  
  if (obj.impact_score_min !== undefined) {
    if (typeof obj.impact_score_min !== "number" || obj.impact_score_min < 0 || obj.impact_score_min > 10) {
      return Err(createValidationError("impact_score_min", "Must be a number between 0 and 10"));
    }
    result.impact_score_min = obj.impact_score_min;
  }
  
  if (obj.search_text !== undefined) {
    if (typeof obj.search_text !== "string") {
      return Err(createValidationError("search_text", "Must be a string"));
    }
    result.search_text = obj.search_text;
  }
  
  if (obj.languages !== undefined) {
    if (!Array.isArray(obj.languages)) {
      return Err(createValidationError("languages", "Must be an array"));
    }
    for (const lang of obj.languages) {
      const langResult = validateLanguageCode(lang);
      if (!langResult.success) {
        return Err(langResult.error);
      }
    }
    result.languages = obj.languages;
  }
  
  const limit = obj.limit !== undefined ? obj.limit : 10;
  if (typeof limit !== "number" || limit < 1 || limit > 100) {
    return Err(createValidationError("limit", "Must be a number between 1 and 100"));
  }
  result.limit = limit;
  
  const offset = obj.offset !== undefined ? obj.offset : 0;
  if (typeof offset !== "number" || offset < 0) {
    return Err(createValidationError("offset", "Must be a non-negative number"));
  }
  result.offset = offset;
  
  return Ok(result);
};

export const validateChangeFilters = (value: unknown): Result<any, ValidationError> => {
  if (typeof value !== "object" || value === null) {
    return Err(createValidationError("change_filters", "Must be an object"));
  }
  
  const obj = value as Record<string, unknown>;
  const result: Record<string, unknown> = {};
  
  if (obj.priority !== undefined) {
    const priorityResult = validatePriority(obj.priority);
    if (!priorityResult.success) {
      return Err(priorityResult.error);
    }
    result.priority = priorityResult.data;
  }
  
  if (obj.since !== undefined) {
    const date = new Date(obj.since as string);
    if (isNaN(date.getTime())) {
      return Err(createValidationError("since", "Must be a valid date"));
    }
    result.since = date;
  }
  
  if (obj.regulation_id !== undefined) {
    if (typeof obj.regulation_id !== "string" || !obj.regulation_id.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/)) {
      return Err(createValidationError("regulation_id", "Must be a valid UUID"));
    }
    result.regulation_id = obj.regulation_id;
  }
  
  const limit = obj.limit !== undefined ? obj.limit : 20;
  if (typeof limit !== "number" || limit < 1 || limit > 100) {
    return Err(createValidationError("limit", "Must be a number between 1 and 100"));
  }
  result.limit = limit;
  
  const offset = obj.offset !== undefined ? obj.offset : 0;
  if (typeof offset !== "number" || offset < 0) {
    return Err(createValidationError("offset", "Must be a non-negative number"));
  }
  result.offset = offset;
  
  return Ok(result);
};

// Validation functions using functional patterns
export type Validator<T> = (data: unknown) => Result<T, ValidationError>;

// Composite validation for complex objects
export const validateRegulationWithChanges = (data: unknown): Result<{
  regulation: any;
  changes: any[];
}, ValidationError> => {
  if (typeof data !== "object" || data === null) {
    return Err(createValidationError("regulation_with_changes", "Must be an object"));
  }
  
  const obj = data as Record<string, unknown>;
  
  const regulationResult = validateRegulation(obj.regulation);
  if (!regulationResult.success) {
    return Err(regulationResult.error);
  }
  
  if (!Array.isArray(obj.changes)) {
    return Err(createValidationError("changes", "Must be an array"));
  }
  
  for (const change of obj.changes) {
    const changeResult = validateChange(change);
    if (!changeResult.success) {
      return Err(changeResult.error);
    }
  }
  
  return Ok({
    regulation: regulationResult.data,
    changes: obj.changes,
  });
};

// Validation pipeline for request processing
export const validateApiRequest = <T>(
  validator: Validator<T>
) => async (req: Request): Promise<Result<T, Response>> => {
  try {
    const body = await req.json();
    const validationResult = validator(body);
    
    if (!validationResult.success) {
      const errorResponse = new Response(
        JSON.stringify({
          success: false,
          error: "Validation failed",
          details: validationResult.error,
          timestamp: new Date().toISOString(),
        }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
      
      return Err(errorResponse);
    }
    
    return Ok(validationResult.data);
  } catch (error) {
    const errorResponse = new Response(
      JSON.stringify({
        success: false,
        error: "Invalid JSON body",
        timestamp: new Date().toISOString(),
      }),
      {
        status: 400,
        headers: { "Content-Type": "application/json" },
      }
    );
    
    return Err(errorResponse);
  }
};

// Pattern matching for validation results
export const handleValidationResult = <T, R>(
  result: Result<T, ValidationError>,
  onSuccess: (data: T) => R,
  onError: (error: ValidationError) => R
): R => {
  return match(result)
    .with({ success: true }, ({ data }) => onSuccess(data))
    .with({ success: false }, ({ error }) => onError(error))
    .exhaustive();
};

// Validation middleware for different content types
export const validateContentType = (req: Request, allowedTypes: string[]): Result<string, string> => {
  const contentType = req.headers.get("content-type") || "";
  
  const isAllowed = allowedTypes.some(type => contentType.includes(type));
  
  return isAllowed 
    ? Ok(contentType)
    : Err(`Unsupported content type. Allowed: ${allowedTypes.join(", ")}`);
};

// Query parameter validation
export const validateQueryParams = <T>(
  url: URL,
  validator: Validator<T>
): Result<T, ValidationError> => {
  const params: Record<string, unknown> = {};
  
  for (const [key, value] of url.searchParams.entries()) {
    // Try to parse numbers and booleans
    if (value === "true" || value === "false") {
      params[key] = value === "true";
    } else if (!isNaN(Number(value)) && value !== "") {
      params[key] = Number(value);
    } else {
      params[key] = value;
    }
  }
  
  return validator(params);
};

// Sanitization functions
export const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, "") // Remove potential HTML tags
    .substring(0, 1000); // Limit length
};

export const sanitizeObject = <T extends Record<string, unknown>>(obj: T): T => {
  const sanitized: Record<string, unknown> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === "string") {
      sanitized[key] = sanitizeInput(value);
    } else if (value && typeof value === "object") {
      sanitized[key] = sanitizeObject(value as Record<string, unknown>);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized as T;
};