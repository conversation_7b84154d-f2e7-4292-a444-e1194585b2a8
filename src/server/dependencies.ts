// Dependency injection container for services
import type { Result } from "../types/core.ts";
import type { Regulation } from "../types/regulation.ts";
import type { Change, ChangeSubscription } from "../types/change.ts";

// Service interfaces for dependency injection
export type RegulationService = {
  readonly findAll: (filters?: RegulationFilters) => Promise<Result<readonly Regulation[]>>;
  readonly findById: (id: string) => Promise<Result<Regulation>>;
  readonly findByCountry: (countryCode: string) => Promise<Result<readonly Regulation[]>>;
  readonly create: (regulation: RegulationInput) => Promise<Result<Regulation>>;
  readonly update: (id: string, updates: Partial<Regulation>) => Promise<Result<Regulation>>;
  readonly delete: (id: string) => Promise<Result<void>>;
};

export type ChangeService = {
  readonly findAll: (filters?: ChangeFilters) => Promise<Result<readonly Change[]>>;
  readonly findById: (id: string) => Promise<Result<Change>>;
  readonly findByRegulation: (regulationId: string) => Promise<Result<readonly Change[]>>;
  readonly findRecent: (hoursBack?: number) => Promise<Result<readonly Change[]>>;
  readonly markNotified: (id: string) => Promise<Result<void>>;
};

export type SubscriptionService = {
  readonly create: (subscription: Omit<ChangeSubscription, "id" | "created_at" | "updated_at">) => Promise<Result<ChangeSubscription>>;
  readonly findByUser: (userId: string) => Promise<Result<readonly ChangeSubscription[]>>;
  readonly update: (id: string, updates: Partial<ChangeSubscription>) => Promise<Result<ChangeSubscription>>;
  readonly delete: (id: string) => Promise<Result<void>>;
};

export type StatsService = {
  readonly getDashboardStats: () => Promise<Result<DashboardStats>>;
  readonly getRegulationStats: () => Promise<Result<RegulationStats>>;
  readonly getChangeAnalytics: (startDate: Date, endDate: Date) => Promise<Result<ChangeAnalytics>>;
};

export type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error, meta?: Record<string, unknown>) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
  readonly debug: (message: string, meta?: Record<string, unknown>) => void;
};

// Service container type
export type Services = {
  readonly regulation: RegulationService;
  readonly change: ChangeService;
  readonly subscription: SubscriptionService;
  readonly stats: StatsService;
  readonly logger: Logger;
  readonly database?: DatabaseService;
  readonly firecrawl?: FirecrawlService;
  readonly crawling?: CrawlingService;
  readonly jobProcessor?: JobProcessorService;
};

// Filter types
type RegulationFilters = {
  readonly country?: string;
  readonly category?: string;
  readonly priority?: string;
  readonly limit?: number;
  readonly offset?: number;
};

type ChangeFilters = {
  readonly priority?: string;
  readonly since?: string;
  readonly limit?: number;
};

// Stats types
type DashboardStats = {
  readonly overview: {
    readonly totalRegulations: number;
    readonly totalChanges: number;
    readonly recentChanges: number;
    readonly countriesMonitored: number;
  };
  readonly distributions: {
    readonly byCountry: Record<string, number>;
    readonly byCategory: Record<string, number>;
    readonly changesByPriority: Record<string, number>;
  };
  readonly recentActivity: readonly Change[];
};

// Import required types
import type { RegulationInput } from "../types/regulation.ts";
import type { ChangeAnalytics } from "../types/change.ts";
import type { RegulationStats } from "../types/regulation.ts";
import type { FirecrawlService } from "../services/firecrawl.ts";
import type { CrawlingService } from "../services/crawling.ts";
import type { JobProcessorService } from "../services/job-processor.ts";
import type { DatabaseService } from "../services/database.ts";
import { Ok, Err } from "../types/core.ts";

// Mock data (in production, these would come from a database)
const mockRegulations: Regulation[] = [
  {
    id: "reg-1",
    country_code: "US",
    source_agency: "US Trade.gov",
    title: { en: "New Semiconductor Export Controls" },
    description: { en: "Enhanced export controls on advanced semiconductor technology." },
    category: "technical_regulation",
    subcategory: "export_control",
    hs_codes: ["8542", "8541"],
    timeline: {
      effective_date: new Date("2024-01-15"),
      implementation_deadline: new Date("2024-03-15"),
    },
    impact_assessment: { economic: 9, operational: 8, compliance: 9, urgency: 8 },
    related_regulations: [],
    original_language: "en",
    document_metadata: {
      source_url: "https://trade.gov/semiconductor-controls",
      document_hash: "abc123",
      content_type: "text/html",
      file_size: 15420,
      extracted_at: new Date("2024-01-10"),
    },
    version: 1,
    created_at: new Date("2024-01-10"),
    updated_at: new Date("2024-01-10"),
  }
];

const mockChanges: Change[] = [
  {
    id: "change-1",
    regulation_id: "reg-1",
    change_type: "amendment",
    priority: "critical",
    impact_score: { economic: 9, operational: 8, compliance: 9, urgency: 8 },
    summary: { en: "Extended semiconductor export controls" },
    detailed_changes: [{
      field: "hs_codes",
      previous_value: ["8542"],
      new_value: ["8542", "8541"],
      change_description: "Added HS code 8541",
      confidence_score: 0.95,
    }],
    affected_industries: ["Technology", "Electronics"],
    implementation_date: new Date("2024-03-15"),
    stakeholders: ["importers", "exporters"],
    detected_at: new Date("2024-01-12"),
    notification_sent: false,
  }
];

// Create logger implementation
const createLogger = (): Logger => ({
  info: (message: string, meta?: Record<string, unknown>) => {
    console.log(`[INFO] ${message}`, meta ? JSON.stringify(meta) : "");
  },
  error: (message: string, error?: Error, meta?: Record<string, unknown>) => {
    console.error(`[ERROR] ${message}`, error?.message || "", meta ? JSON.stringify(meta) : "");
  },
  warn: (message: string, meta?: Record<string, unknown>) => {
    console.warn(`[WARN] ${message}`, meta ? JSON.stringify(meta) : "");
  },
  debug: (message: string, meta?: Record<string, unknown>) => {
    console.debug(`[DEBUG] ${message}`, meta ? JSON.stringify(meta) : "");
  },
});

// Create regulation service implementation
const createRegulationService = (logger: Logger): RegulationService => ({
  findAll: async (filters?: RegulationFilters) => {
    try {
      let filtered = [...mockRegulations];

      if (filters?.country) {
        filtered = filtered.filter(r => r.country_code === filters.country);
      }
      if (filters?.category) {
        filtered = filtered.filter(r => r.category === filters.category);
      }

      const offset = filters?.offset || 0;
      const limit = filters?.limit || 10;
      const paginated = filtered.slice(offset, offset + limit);

      logger.info(`Retrieved ${paginated.length} regulations`, { filters });
      return Ok(paginated);
    } catch (error) {
      logger.error("Failed to retrieve regulations", error as Error);
      return Err(error as Error);
    }
  },

  findById: async (id: string) => {
    try {
      const regulation = mockRegulations.find(r => r.id === id);
      if (!regulation) {
        return Err(new Error(`Regulation not found: ${id}`));
      }
      return Ok(regulation);
    } catch (error) {
      return Err(error as Error);
    }
  },

  findByCountry: async (countryCode: string) => {
    try {
      const regulations = mockRegulations.filter(r => r.country_code === countryCode);
      return Ok(regulations);
    } catch (error) {
      return Err(error as Error);
    }
  },

  create: async () => {
    return Err(new Error("Not implemented"));
  },

  update: async () => {
    return Err(new Error("Not implemented"));
  },

  delete: async () => {
    return Err(new Error("Not implemented"));
  },
});

// Create change service implementation
const createChangeService = (logger: Logger): ChangeService => ({
  findAll: async (filters?: ChangeFilters) => {
    try {
      let filtered = [...mockChanges];

      if (filters?.priority) {
        filtered = filtered.filter(c => c.priority === filters.priority);
      }
      if (filters?.since) {
        const sinceDate = new Date(filters.since);
        filtered = filtered.filter(c => c.detected_at > sinceDate);
      }

      const limit = filters?.limit || 20;
      const paginated = filtered.slice(0, limit);

      logger.info(`Retrieved ${paginated.length} changes`, { filters });
      return Ok(paginated);
    } catch (error) {
      logger.error("Failed to retrieve changes", error as Error);
      return Err(error as Error);
    }
  },

  findById: async (id: string) => {
    try {
      const change = mockChanges.find(c => c.id === id);
      if (!change) {
        return Err(new Error(`Change not found: ${id}`));
      }
      return Ok(change);
    } catch (error) {
      return Err(error as Error);
    }
  },

  findByRegulation: async (regulationId: string) => {
    try {
      const changes = mockChanges.filter(c => c.regulation_id === regulationId);
      return Ok(changes);
    } catch (error) {
      return Err(error as Error);
    }
  },

  findRecent: async (hoursBack = 24) => {
    try {
      const cutoffTime = new Date(Date.now() - hoursBack * 60 * 60 * 1000);
      const recentChanges = mockChanges.filter(c => c.detected_at > cutoffTime);
      return Ok(recentChanges);
    } catch (error) {
      return Err(error as Error);
    }
  },

  markNotified: async (id: string) => {
    try {
      const change = mockChanges.find(c => c.id === id);
      if (!change) {
        return Err(new Error(`Change not found: ${id}`));
      }
      // In a real implementation, update the database
      return Ok(undefined);
    } catch (error) {
      return Err(error as Error);
    }
  },
});

// Create subscription service implementation
const createSubscriptionService = (logger: Logger): SubscriptionService => ({
  create: async (subscriptionData) => {
    try {
      const subscription: ChangeSubscription = {
        ...subscriptionData,
        id: crypto.randomUUID(),
        created_at: new Date(),
        updated_at: new Date(),
      };

      logger.info(`Created subscription ${subscription.id}`);
      return Ok(subscription);
    } catch (error) {
      logger.error("Failed to create subscription", error as Error);
      return Err(error as Error);
    }
  },

  findByUser: async () => {
    return Ok([]);
  },

  update: async () => {
    return Err(new Error("Not implemented"));
  },

  delete: async () => {
    return Err(new Error("Not implemented"));
  },
});

// Create stats service implementation
const createStatsService = (logger: Logger): StatsService => ({
  getDashboardStats: async () => {
    try {
      const totalRegulations = mockRegulations.length;
      const totalChanges = mockChanges.length;
      const recentChanges = mockChanges.filter(c =>
        c.detected_at > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      );

      const countryCounts = mockRegulations.reduce((acc, reg) => {
        acc[reg.country_code] = (acc[reg.country_code] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const categoryCounts = mockRegulations.reduce((acc, reg) => {
        acc[reg.category] = (acc[reg.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const priorityCounts = mockChanges.reduce((acc, change) => {
        acc[change.priority] = (acc[change.priority] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const stats: DashboardStats = {
        overview: {
          totalRegulations,
          totalChanges,
          recentChanges: recentChanges.length,
          countriesMonitored: Object.keys(countryCounts).length,
        },
        distributions: {
          byCountry: countryCounts,
          byCategory: categoryCounts,
          changesByPriority: priorityCounts,
        },
        recentActivity: recentChanges.slice(0, 5),
      };

      return Ok(stats);
    } catch (error) {
      logger.error("Failed to get dashboard stats", error as Error);
      return Err(error as Error);
    }
  },

  getRegulationStats: async () => {
    return Err(new Error("Not implemented"));
  },

  getChangeAnalytics: async () => {
    return Err(new Error("Not implemented"));
  },
});

// Main factory function
export const createServices = (config?: { firecrawlApiKey?: string }): Services => {
  const logger = createLogger();

  return {
    regulation: createRegulationService(logger),
    change: createChangeService(logger),
    subscription: createSubscriptionService(logger),
    stats: createStatsService(logger),
    logger,
  };
};

// Async factory function with Firecrawl support
export const createServicesWithFirecrawl = async (config: { firecrawlApiKey: string }): Promise<Services> => {
  const logger = createLogger();

  let services: Partial<Services> = {
    regulation: createRegulationService(logger),
    change: createChangeService(logger),
    subscription: createSubscriptionService(logger),
    stats: createStatsService(logger),
    logger,
  };

  try {
    // Initialize database first (optional)
    let database: DatabaseService | undefined;

    try {
      const { createDatabaseService } = await import("../services/database.ts");
      const databaseResult = await createDatabaseService({}, logger);

      if (databaseResult.success) {
        database = databaseResult.data;
        logger.info("Database service initialized successfully");
      } else {
        logger.warn("Database service not available, using in-memory storage", { error: databaseResult.error.message });
      }
    } catch (dbError) {
      logger.warn("Database service not available, using in-memory storage", { error: (dbError as Error).message });
    }

    const { createFirecrawlService } = await import("../services/firecrawl.ts");
    const { createCrawlingService } = await import("../services/crawling.ts");
    const { createJobProcessorService } = await import("../services/job-processor.ts");

    const firecrawlService = createFirecrawlService({
      apiKey: config.firecrawlApiKey,
    });

    const crawlingService = createCrawlingService(firecrawlService, logger, database);

    const jobProcessorService = createJobProcessorService(
      crawlingService,
      services.regulation!,
      logger
    );

    services = {
      ...services,
      database,
      firecrawl: firecrawlService,
      crawling: crawlingService,
      jobProcessor: jobProcessorService,
    };

    // Start the job processor
    jobProcessorService.start();

    logger.info("Services initialized with Deno KV database and job processor");
  } catch (error) {
    logger.error("Failed to initialize services", error as Error);
  }

  return services as Services;
};