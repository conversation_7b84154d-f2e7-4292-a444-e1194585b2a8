// API routes for crawling and web source management
import { match } from "ts-pattern";
import { get, post, put, del, createJsonResponse, createErrorJsonResponse } from "../lib/router.ts";
import type { Services } from "./dependencies.ts";
import type { RouteConfig, RouteParams } from "../lib/router.ts";
import { isOk, isErr } from "../types/core.ts";
import type { WebSource, CrawlJob } from "../services/firecrawl.ts";
import { getWebSources, getWebSourceById } from "../data/web-sources.ts";

// Create crawling routes with dependency injection
export const createCrawlingRoutes = (services: Services): readonly RouteConfig[] => [
  // Web sources management
  get("/api/crawl/sources", createGetWebSourcesHandler(services)),
  post("/api/crawl/sources", createPostWebSourceHandler(services)),
  put("/api/crawl/sources/([^/]+)", createPutWebSourceHandler(services)),
  del("/api/crawl/sources/([^/]+)", createDeleteWebSourceHandler(services)),
  
  // Crawl jobs management
  get("/api/crawl/jobs", createGetCrawlJobsHandler(services)),
  post("/api/crawl/jobs", createPostCrawlJobHandler(services)),
  get("/api/crawl/jobs/([^/]+)", createGetCrawlJobHandler(services)),
  post("/api/crawl/jobs/([^/]+)/process", createProcessJobHandler(services)),
  
  // Crawling operations
  post("/api/crawl/start", createStartCrawlHandler(services)),
  post("/api/crawl/start-all", createStartAllCrawlsHandler(services)),
];

// Web sources handlers
const createGetWebSourcesHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const url = new URL(req.url);
      const active = url.searchParams.get("active");
      const country = url.searchParams.get("country");
      
      // Get web sources from shared module
      const sources = getWebSources({ active: active === "true", country });
      services.logger.info(`Retrieved ${sources.length} web sources`, { 
        active: active === "true", 
        country 
      });
      
      return createJsonResponse({
        sources,
        count: sources.length,
      });
    } catch (error) {
      services.logger.error("Failed to get web sources", error as Error);
      return createErrorJsonResponse("Failed to retrieve web sources", 500);
    }
  };
};

const createPostWebSourceHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const body = await req.json();
      
      // Validate web source data
      const validation = validateWebSourceInput(body);
      if (!validation.success) {
        return createErrorJsonResponse(validation.error, 400);
      }
      
      const webSource: WebSource = {
        id: crypto.randomUUID(),
        ...body,
        lastCrawled: undefined,
        active: body.active ?? true,
      };
      
      // TODO: Save to database
      services.logger.info(`Created web source: ${webSource.name}`, { 
        webSourceId: webSource.id 
      });
      
      return createJsonResponse(webSource, 201);
    } catch (error) {
      services.logger.error("Failed to create web source", error as Error);
      return createErrorJsonResponse("Failed to create web source", 500);
    }
  };
};

const createPutWebSourceHandler = (services: Services) => {
  return async (req: Request, params: RouteParams): Promise<Response> => {
    try {
      const sourceId = params.param1;
      const body = await req.json();
      
      // TODO: Update web source in database
      services.logger.info(`Updated web source: ${sourceId}`);
      
      return createJsonResponse({ success: true });
    } catch (error) {
      services.logger.error("Failed to update web source", error as Error);
      return createErrorJsonResponse("Failed to update web source", 500);
    }
  };
};

const createDeleteWebSourceHandler = (services: Services) => {
  return async (req: Request, params: RouteParams): Promise<Response> => {
    try {
      const sourceId = params.param1;
      
      // TODO: Delete web source from database
      services.logger.info(`Deleted web source: ${sourceId}`);
      
      return createJsonResponse({ success: true });
    } catch (error) {
      services.logger.error("Failed to delete web source", error as Error);
      return createErrorJsonResponse("Failed to delete web source", 500);
    }
  };
};

// Crawl jobs handlers
const createGetCrawlJobsHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const url = new URL(req.url);
      const status = url.searchParams.get("status");
      const limit = parseInt(url.searchParams.get("limit") || "20");
      
      // TODO: Get crawl jobs from database
      const jobs = getCrawlJobs({ status, limit });
      
      return createJsonResponse({
        jobs,
        count: jobs.length,
      });
    } catch (error) {
      services.logger.error("Failed to get crawl jobs", error as Error);
      return createErrorJsonResponse("Failed to retrieve crawl jobs", 500);
    }
  };
};

const createPostCrawlJobHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const body = await req.json();
      const { webSourceId } = body;
      
      if (!webSourceId) {
        return createErrorJsonResponse("Web source ID is required", 400);
      }
      
      // TODO: Get web source and start crawl job
      const webSource = getWebSourceById(webSourceId);
      if (!webSource) {
        return createErrorJsonResponse("Web source not found", 404);
      }
      
      // TODO: Use actual crawling service
      const job: CrawlJob = {
        id: crypto.randomUUID(),
        webSourceId,
        status: "pending",
        startedAt: new Date(),
        pagesFound: 0,
        regulationsExtracted: 0,
        changesDetected: 0,
      };
      
      services.logger.info(`Created crawl job`, { jobId: job.id, webSourceId });
      
      return createJsonResponse(job, 201);
    } catch (error) {
      services.logger.error("Failed to create crawl job", error as Error);
      return createErrorJsonResponse("Failed to create crawl job", 500);
    }
  };
};

const createGetCrawlJobHandler = (services: Services) => {
  return async (req: Request, params: RouteParams): Promise<Response> => {
    try {
      const jobId = params.param1;
      
      // TODO: Get job status from crawling service
      const job = getCrawlJobById(jobId);
      if (!job) {
        return createErrorJsonResponse("Crawl job not found", 404);
      }
      
      return createJsonResponse(job);
    } catch (error) {
      services.logger.error("Failed to get crawl job", error as Error);
      return createErrorJsonResponse("Failed to retrieve crawl job", 500);
    }
  };
};

const createProcessJobHandler = (services: Services) => {
  return async (req: Request, params: RouteParams): Promise<Response> => {
    try {
      const jobId = params.param1;
      
      // TODO: Process completed job with crawling service
      services.logger.info(`Processing crawl job: ${jobId}`);
      
      return createJsonResponse({ 
        success: true, 
        message: "Job processing started" 
      });
    } catch (error) {
      services.logger.error("Failed to process crawl job", error as Error);
      return createErrorJsonResponse("Failed to process crawl job", 500);
    }
  };
};

// Crawling operation handlers
const createStartCrawlHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      const body = await req.json();
      const { webSourceId } = body;
      
      if (!webSourceId) {
        return createErrorJsonResponse("Web source ID is required", 400);
      }
      
      // TODO: Start crawl using crawling service
      services.logger.info(`Starting crawl for web source: ${webSourceId}`);
      
      const job: CrawlJob = {
        id: crypto.randomUUID(),
        webSourceId,
        status: "running",
        startedAt: new Date(),
        pagesFound: 0,
        regulationsExtracted: 0,
        changesDetected: 0,
      };
      
      return createJsonResponse(job);
    } catch (error) {
      services.logger.error("Failed to start crawl", error as Error);
      return createErrorJsonResponse("Failed to start crawl", 500);
    }
  };
};

const createStartAllCrawlsHandler = (services: Services) => {
  return async (req: Request, _params: RouteParams): Promise<Response> => {
    try {
      services.logger.info("Starting crawl for all active web sources");
      
      const jobs: CrawlJob[] = [];
      
      if (services.crawling) {
        // Get all active web sources
        const sources = getWebSources({ active: true });
        
        // Start crawl jobs for each source
        for (const source of sources) {
          try {
            const jobResult = await services.crawling.startCrawlJob(source);
            if (isOk(jobResult)) {
              jobs.push(jobResult.data);
              services.logger.info(`Started crawl job for ${source.name}`, { 
                jobId: jobResult.data.id,
                webSourceId: source.id 
              });
            } else {
              services.logger.error(`Failed to start crawl for ${source.name}`, jobResult.error);
            }
          } catch (error) {
            services.logger.error(`Error starting crawl for ${source.name}`, error as Error);
          }
        }
      } else {
        services.logger.warn("Crawling service not available - Firecrawl not configured");
        return createErrorJsonResponse("Crawling service not available", 503);
      }
      
      return createJsonResponse({
        message: `Started ${jobs.length} crawl jobs for active sources`,
        jobs,
        count: jobs.length,
      });
    } catch (error) {
      services.logger.error("Failed to start all crawls", error as Error);
      return createErrorJsonResponse("Failed to start crawls", 500);
    }
  };
};

// Helper functions

const getCrawlJobs = (filters: { status?: string | null; limit: number }): CrawlJob[] => {
  // Mock data - replace with actual database query
  return [];
};

// getWebSourceById imported from web-sources.ts

const getCrawlJobById = (id: string): CrawlJob | null => {
  // Mock data - replace with actual database query
  return null;
};

const validateWebSourceInput = (data: any): { success: boolean; error?: string } => {
  if (!data.name || typeof data.name !== "string") {
    return { success: false, error: "Name is required and must be a string" };
  }
  
  if (!data.url || typeof data.url !== "string") {
    return { success: false, error: "URL is required and must be a string" };
  }
  
  if (!data.country || typeof data.country !== "string") {
    return { success: false, error: "Country is required and must be a string" };
  }
  
  if (!data.agency || typeof data.agency !== "string") {
    return { success: false, error: "Agency is required and must be a string" };
  }
  
  return { success: true };
};