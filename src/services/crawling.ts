// Crawling service for regulation change monitoring
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type {
  FirecrawlService,
  WebSource,
  CrawledPage
} from "./firecrawl.ts";
import type { Regulation, RegulationInput } from "../types/regulation.ts";
import type { Change } from "../types/change.ts";
import { getActiveWebSources, getWebSourceById } from "../data/web-sources.ts";

// Crawl job types
export type CrawlJob = {
  readonly id: string;
  readonly webSourceId: string;
  readonly status: "pending" | "running" | "completed" | "failed";
  readonly firecrawlJobId?: string;
  readonly startedAt?: Date;
  readonly completedAt?: Date;
  readonly pagesFound: number;
  readonly regulationsExtracted: number;
  readonly changesDetected: number;
  readonly error?: string;
};

export type CrawlJobResult = {
  readonly job: CrawlJob;
  readonly regulations: readonly RegulationInput[];
  readonly changes: readonly Change[];
};

// Content analysis types
export type ExtractedRegulation = {
  readonly title: string;
  readonly description: string;
  readonly category: string;
  readonly effectiveDate?: Date;
  readonly sourceUrl: string;
  readonly confidence: number;
};

export type RegulationChange = {
  readonly type: "new" | "modified" | "removed";
  readonly regulation: ExtractedRegulation;
  readonly changeDescription: string;
  readonly confidence: number;
};

// Crawling service interface
export type CrawlingService = {
  readonly startCrawlJob: (webSource: WebSource) => Promise<Result<CrawlJob>>;
  readonly getJobStatus: (jobId: string) => Promise<Result<CrawlJob>>;
  readonly processCompletedJob: (jobId: string) => Promise<Result<CrawlJobResult>>;
  readonly scheduleAllJobs: () => Promise<Result<readonly CrawlJob[]>>;
  readonly extractRegulations: (pages: readonly CrawledPage[], source: WebSource) => Promise<Result<readonly ExtractedRegulation[]>>;
  readonly detectChanges: (newRegulations: readonly ExtractedRegulation[], existing: readonly Regulation[]) => Promise<Result<readonly RegulationChange[]>>;
};

// Create crawling service implementation
export const createCrawlingService = (
  firecrawl: FirecrawlService,
  logger: Logger,
  database?: { saveJob: (job: CrawlJob) => Promise<Result<void>>; getJob: (id: string) => Promise<Result<CrawlJob>>; updateJobStatus: (id: string, status: CrawlJob["status"], error?: string) => Promise<Result<void>> }
): CrawlingService => {
  // Fallback to in-memory storage if no database provided
  const jobs = new Map<string, CrawlJob>();

  const service: CrawlingService = {
    startCrawlJob: async (webSource: WebSource) => {
      try {
        logger.info(`Starting crawl job for ${webSource.name}`, {
          webSourceId: webSource.id,
          url: webSource.url
        });

        // Start Firecrawl job
        const crawlRequest = {
          url: webSource.url,
          crawlerOptions: webSource.crawlConfig,
          pageOptions: {
            onlyMainContent: true,
            includeHtml: false,
          },
        };

        const firecrawlResult = await firecrawl.crawlWebsite(crawlRequest);
        if (!firecrawlResult.success) {
          return Err(firecrawlResult.error);
        }

        const job: CrawlJob = {
          id: crypto.randomUUID(),
          webSourceId: webSource.id,
          status: "running",
          firecrawlJobId: firecrawlResult.data.jobId,
          startedAt: new Date(),
          pagesFound: 0,
          regulationsExtracted: 0,
          changesDetected: 0,
        };

        // Save job to database or fallback to memory
        if (database) {
          const saveResult = await database.saveJob(job);
          if (!saveResult.success) {
            logger.error("Failed to save job to database", saveResult.error, { jobId: job.id });
            // Fallback to memory storage
            jobs.set(job.id, job);
          }
        } else {
          jobs.set(job.id, job);
        }

        logger.info(`Crawl job started`, {
          jobId: job.id,
          firecrawlJobId: job.firecrawlJobId
        });

        return Ok(job);
      } catch (error) {
        logger.error("Failed to start crawl job", error as Error, {
          webSourceId: webSource.id
        });
        return Err(error as Error);
      }
    },

    getJobStatus: async (jobId: string) => {
      // Try database first, fallback to memory
      let job: CrawlJob | undefined;

      if (database) {
        const dbResult = await database.getJob(jobId);
        if (dbResult.success) {
          job = dbResult.data;
        }
      }

      if (!job) {
        job = jobs.get(jobId);
      }

      if (!job) {
        return Err(new Error(`Job not found: ${jobId}`));
      }

      // If job is still running, check Firecrawl status
      if (job.status === "running" && job.firecrawlJobId) {
        const statusResult = await firecrawl.getJobStatus(job.firecrawlJobId);
        if (statusResult.success) {
          const firecrawlStatus = statusResult.data;

          const updatedJob: CrawlJob = {
            ...job,
            status: firecrawlStatus.status === "completed" ? "completed" :
              firecrawlStatus.status === "failed" ? "failed" : "running",
            pagesFound: firecrawlStatus.total || job.pagesFound,
            completedAt: firecrawlStatus.status === "completed" || firecrawlStatus.status === "failed"
              ? new Date() : job.completedAt,
          };

          jobs.set(jobId, updatedJob);
          return Ok(updatedJob);
        }
      }

      return Ok(job);
    },

    processCompletedJob: async (jobId: string) => {
      const job = jobs.get(jobId);
      if (!job) {
        return Err(new Error(`Job not found: ${jobId}`));
      }

      if (job.status !== "completed" || !job.firecrawlJobId) {
        return Err(new Error(`Job is not completed: ${job.status}`));
      }

      try {
        logger.info(`Processing completed job`, { jobId });

        // Get crawled data from Firecrawl
        const statusResult = await firecrawl.getJobStatus(job.firecrawlJobId);
        if (!statusResult.success || !statusResult.data.data) {
          return Err(new Error("Failed to get crawled data"));
        }

        const pages = statusResult.data.data;

        // TODO: Get web source configuration
        const webSource = getWebSourceById(job.webSourceId);
        if (!webSource) {
          return Err(new Error(`Web source not found: ${job.webSourceId}`));
        }

        // Extract regulations from crawled pages
        const extractResult = await service.extractRegulations(pages, webSource);
        if (!extractResult.success) {
          return Err(extractResult.error);
        }

        const extractedRegulations = extractResult.data;

        // Convert to regulation inputs
        const regulations: RegulationInput[] = extractedRegulations.map(reg => ({
          country_code: webSource.country,
          source_agency: webSource.agency,
          title: { en: reg.title },
          description: { en: reg.description },
          category: reg.category as any,
          subcategory: "general",
          hs_codes: [],
          timeline: {
            effective_date: reg.effectiveDate || new Date(),
          },
          impact_assessment: {
            economic: Math.round(reg.confidence * 10),
            operational: 5,
            compliance: 7,
            urgency: 5,
          },
          related_regulations: [],
          original_language: "en",
          document_metadata: {
            source_url: reg.sourceUrl,
            document_hash: crypto.randomUUID(),
            content_type: "text/html",
            file_size: reg.description.length,
            extracted_at: new Date(),
          },
        }));

        // TODO: Detect changes against existing regulations
        const changes: Change[] = [];

        const result: CrawlJobResult = {
          job: {
            ...job,
            regulationsExtracted: regulations.length,
            changesDetected: changes.length,
          },
          regulations,
          changes,
        };

        jobs.set(jobId, result.job);

        logger.info(`Job processing completed`, {
          jobId,
          regulationsExtracted: regulations.length,
          changesDetected: changes.length,
        });

        return Ok(result);
      } catch (error) {
        logger.error("Failed to process completed job", error as Error, { jobId });

        const failedJob = {
          ...job,
          status: "failed" as const,
          error: (error as Error).message,
          completedAt: new Date(),
        };

        jobs.set(jobId, failedJob);
        return Err(error as Error);
      }
    },

    scheduleAllJobs: async () => {
      logger.info("Scheduling crawl jobs for all active web sources");

      const sources = getActiveWebSources();
      const results: CrawlJob[] = [];

      for (const source of sources) {
        const result = await service.startCrawlJob(source);
        if (result.success) {
          results.push(result.data);
        } else {
          logger.error(`Failed to start job for ${source.name}`, result.error);
        }
      }

      return Ok(results);
    },

    extractRegulations: async (pages: readonly CrawledPage[], source: WebSource) => {
      logger.info(`Extracting regulations from ${pages.length} pages`, {
        webSourceId: source.id
      });

      const regulations: ExtractedRegulation[] = [];

      for (const page of pages) {
        // Simple pattern-based extraction (in production, use AI/ML)
        const content = page.markdown.toLowerCase();

        // Look for regulation indicators
        const hasRegulationKeywords = [
          'regulation', 'rule', 'policy', 'measure', 'directive',
          'tariff', 'trade', 'import', 'export', 'customs'
        ].some(keyword => content.includes(keyword));

        if (hasRegulationKeywords && page.metadata.title) {
          const regulation: ExtractedRegulation = {
            title: page.metadata.title,
            description: page.metadata.description ||
              page.markdown.substring(0, 500) + "...",
            category: determineCategoryFromContent(content, source.category),
            effectiveDate: extractDateFromContent(page.markdown),
            sourceUrl: page.url,
            confidence: calculateConfidence(page, source),
          };

          regulations.push(regulation);
        }
      }

      logger.info(`Extracted ${regulations.length} potential regulations`);
      return Ok(regulations);
    },

    detectChanges: async (newRegulations: readonly ExtractedRegulation[], existing: readonly Regulation[]) => {
      logger.info(`Detecting changes between ${newRegulations.length} new and ${existing.length} existing regulations`);

      const changes: RegulationChange[] = [];

      // Simple change detection based on title similarity
      for (const newReg of newRegulations) {
        const existingMatch = existing.find(existing =>
          calculateSimilarity(newReg.title, existing.title.en || "") > 0.8
        );

        if (!existingMatch) {
          // New regulation
          changes.push({
            type: "new",
            regulation: newReg,
            changeDescription: `New regulation detected: ${newReg.title}`,
            confidence: newReg.confidence,
          });
        }
        // TODO: Add logic for modified regulations
      }

      return Ok(changes);
    },
  };

  return service;
};

// Helper functions - imported from shared web-sources module

const determineCategoryFromContent = (content: string, sourceCategories: string[]): string => {
  // Simple keyword matching
  if (content.includes('tariff')) return 'tariff';
  if (content.includes('technical')) return 'technical_regulation';
  if (content.includes('sanitary') || content.includes('phytosanitary')) return 'sanitary_phytosanitary';

  return sourceCategories[0] || 'technical_regulation';
};

const extractDateFromContent = (content: string): Date | undefined => {
  // Simple date extraction (in production, use proper date parsing)
  const dateRegex = /(\d{4}-\d{2}-\d{2})|(\d{1,2}\/\d{1,2}\/\d{4})|(\w+ \d{1,2}, \d{4})/;
  const match = content.match(dateRegex);

  if (match) {
    const date = new Date(match[0]);
    return isNaN(date.getTime()) ? undefined : date;
  }

  return undefined;
};

const calculateConfidence = (page: CrawledPage, source: WebSource): number => {
  let confidence = 0.5; // Base confidence

  // Higher confidence for official government sources
  if (source.url.includes('.gov') || source.url.includes('.europa.eu')) {
    confidence += 0.3;
  }

  // Higher confidence for pages with good metadata
  if (page.metadata.title && page.metadata.description) {
    confidence += 0.1;
  }

  // Higher confidence for recent content
  if (page.metadata.publishedDate) {
    const publishDate = new Date(page.metadata.publishedDate);
    const daysSincePublish = (Date.now() - publishDate.getTime()) / (1000 * 60 * 60 * 24);
    if (daysSincePublish < 30) {
      confidence += 0.1;
    }
  }

  return Math.min(confidence, 1.0);
};

const calculateSimilarity = (str1: string, str2: string): number => {
  // Simple similarity calculation (in production, use proper text similarity)
  const words1 = str1.toLowerCase().split(/\s+/);
  const words2 = str2.toLowerCase().split(/\s+/);

  const commonWords = words1.filter(word => words2.includes(word));
  const totalWords = new Set([...words1, ...words2]).size;

  return commonWords.length / totalWords;
};

// Logger interface (should match existing logger)
type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error, meta?: Record<string, unknown>) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
  readonly debug: (message: string, meta?: Record<string, unknown>) => void;
};