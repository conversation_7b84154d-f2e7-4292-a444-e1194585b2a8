// Firecrawl service for crawling regulation changes from web sources
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";

// Firecrawl API types
export type FirecrawlConfig = {
  readonly apiKey: string;
  readonly baseUrl?: string;
};

export type CrawlRequest = {
  readonly url: string;
  readonly crawlerOptions?: {
    readonly includes?: string[];
    readonly excludes?: string[];
    readonly maxDepth?: number;
    readonly limit?: number;
  };
  readonly pageOptions?: {
    readonly onlyMainContent?: boolean;
    readonly includeHtml?: boolean;
    readonly screenshot?: boolean;
  };
  readonly extractorOptions?: {
    readonly mode?: "llm-extraction";
    readonly extractionPrompt?: string;
    readonly extractionSchema?: Record<string, unknown>;
  };
};

export type CrawlResult = {
  readonly success: boolean;
  readonly jobId?: string;
  readonly data?: CrawledPage[];
  readonly error?: string;
};

export type CrawledPage = {
  readonly url: string;
  readonly markdown: string;
  readonly html?: string;
  readonly extract?: Record<string, unknown>; // Firecrawl extracted structured data
  readonly metadata: {
    readonly title: string;
    readonly description?: string;
    readonly keywords?: string[];
    readonly author?: string;
    readonly publishedDate?: string;
    readonly modifiedDate?: string;
  };
  readonly screenshot?: string;
};

export type JobStatus = {
  readonly status: "scraping" | "completed" | "failed";
  readonly current?: number;
  readonly total?: number;
  readonly creditsUsed?: number;
  readonly expiresAt?: string;
  readonly data?: CrawledPage[];
};

// Web source configuration for regulation monitoring
export type WebSource = {
  readonly id: string;
  readonly name: string;
  readonly url: string;
  readonly country: string;
  readonly agency: string;
  readonly category: string[];
  readonly crawlConfig: {
    readonly includes?: string[];
    readonly excludes?: string[];
    readonly maxDepth: number;
    readonly limit: number;
  };
  readonly schedule: string; // cron expression
  readonly lastCrawled?: Date;
  readonly active: boolean;
};

// Firecrawl service interface
export type FirecrawlService = {
  readonly scrapeUrl: (url: string, options?: CrawlRequest['pageOptions']) => Promise<Result<CrawledPage>>;
  readonly crawlWebsite: (request: CrawlRequest) => Promise<Result<CrawlResult>>;
  readonly getJobStatus: (jobId: string) => Promise<Result<JobStatus>>;
  readonly cancelJob: (jobId: string) => Promise<Result<void>>;
};

// Create Firecrawl service implementation
export const createFirecrawlService = (config: FirecrawlConfig): FirecrawlService => {
  const baseUrl = config.baseUrl || "https://api.firecrawl.dev";

  const makeRequest = async <T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<Result<T>> => {
    try {
      const response = await fetch(`${baseUrl}${endpoint}`, {
        ...options,
        headers: {
          "Authorization": `Bearer ${config.apiKey}`,
          "Content-Type": "application/json",
          ...options.headers,
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        return Err(new Error(`Firecrawl API error ${response.status}: ${errorText}`));
      }

      const data = await response.json();
      return Ok(data);
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  };

  return {
    scrapeUrl: async (url: string, options?: CrawlRequest['pageOptions']) => {
      const result = await makeRequest<{ success: boolean; data: CrawledPage }>("/v0/scrape", {
        method: "POST",
        body: JSON.stringify({
          url,
          pageOptions: {
            onlyMainContent: true,
            includeHtml: false,
            ...options,
          },
        }),
      });

      if (!result.success) {
        return result as Result<CrawledPage>;
      }

      if (!result.data.success) {
        return Err(new Error("Firecrawl scrape failed"));
      }

      return Ok(result.data.data);
    },

    crawlWebsite: async (request: CrawlRequest) => {
      const requestBody: Record<string, unknown> = {
        url: request.url,
        crawlerOptions: {
          includes: [],
          excludes: [],
          maxDepth: 2,
          limit: 100,
          ...request.crawlerOptions,
        },
        pageOptions: {
          onlyMainContent: true,
          includeHtml: false,
          ...request.pageOptions,
        },
      };

      // Add extraction options if provided
      if (request.extractorOptions) {
        requestBody["extractorOptions"] = request.extractorOptions;
      }

      const result = await makeRequest<CrawlResult>("/v0/crawl", {
        method: "POST",
        body: JSON.stringify(requestBody),
      });

      return result;
    },

    getJobStatus: async (jobId: string) => {
      return await makeRequest<JobStatus>(`/v0/crawl/status/${jobId}`);
    },

    cancelJob: async (jobId: string) => {
      const result = await makeRequest<{ success: boolean }>(`/v0/crawl/cancel/${jobId}`, {
        method: "DELETE",
      });

      if (!result.success) {
        return result as Result<void>;
      }

      return Ok(undefined);
    },
  };
};

// Predefined web sources for regulation monitoring
export const getDefaultWebSources = (): readonly WebSource[] => [
  {
    id: "us-trade-gov",
    name: "US Trade.gov News",
    url: "https://www.trade.gov/news",
    country: "US",
    agency: "Department of Commerce",
    category: ["trade_remedies", "export_control", "technical_regulation"],
    crawlConfig: {
      includes: ["**/news/**", "**/press-releases/**", "**/trade-policy/**"],
      excludes: ["**/archive/**", "**/pdf/**", "**/events/**"],
      maxDepth: 2,
      limit: 10,
    },
    schedule: "0 */6 * * *", // Every 6 hours
    active: true,
  },
  // {
  //   id: "eu-commission-trade",
  //   name: "EU Commission Trade Policy",
  //   url: "https://policy.trade.ec.europa.eu",
  //   country: "EU",
  //   agency: "European Commission",
  //   category: ["tariff", "non_tariff_barrier", "trade_remedies"],
  //   crawlConfig: {
  //     includes: ["**/policy/**", "**/measures/**", "**/news/**"],
  //     excludes: ["**/documents/**", "**/archive/**"],
  //     maxDepth: 2,
  //     limit: 30,
  //   },
  //   schedule: "0 */8 * * *", // Every 8 hours
  //   active: true,
  // },
  // {
  //   id: "wto-news",
  //   name: "WTO News and Updates",
  //   url: "https://www.wto.org/english/news_e/news_e.htm",
  //   country: "GLOBAL",
  //   agency: "World Trade Organization",
  //   category: ["trade_facilitation", "services_regulation", "intellectual_property"],
  //   crawlConfig: {
  //     includes: ["**/news/**", "**/press/**"],
  //     excludes: ["**/archive/**", "**/old/**"],
  //     maxDepth: 2,
  //     limit: 25,
  //   },
  //   schedule: "0 */12 * * *", // Every 12 hours
  //   active: true,
  // },
  // {
  //   id: "uk-gov-trade",
  //   name: "UK Government Trade Policy",
  //   url: "https://www.gov.uk/government/policies/uk-trade-policy",
  //   country: "GB",
  //   agency: "Department for International Trade",
  //   category: ["tariff", "trade_remedies", "services_regulation"],
  //   crawlConfig: {
  //     includes: ["**/policies/**", "**/news/**", "**/guidance/**"],
  //     excludes: ["**/consultations/**", "**/statistics/**"],
  //     maxDepth: 2,
  //     limit: 40,
  //   },
  //   schedule: "0 */8 * * *", // Every 8 hours
  //   active: true,
  // },
];