// Notification Service - Webhook Support and Priority-based Routing

import type { Result, Priority } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type { 
  Change, 
  NotificationChannel, 
  ChangeSubscription 
} from "../types/change.ts";
import type { Regulation } from "../types/regulation.ts";

// Notification payload structure
export type NotificationPayload = {
  readonly id: string;
  readonly type: "regulation_change" | "new_regulation" | "system_alert";
  readonly priority: Priority;
  readonly timestamp: Date;
  readonly regulation: Regulation;
  readonly changes?: readonly Change[];
  readonly metadata: NotificationMetadata;
};

// Notification metadata
export type NotificationMetadata = {
  readonly source: string;
  readonly version: string;
  readonly retry_count: number;
  readonly correlation_id: string;
  readonly tags: readonly string[];
};

// Notification delivery result
export type DeliveryResult = {
  readonly channel: NotificationChannel;
  readonly success: boolean;
  readonly delivered_at?: Date;
  readonly error?: string;
  readonly response_time_ms: number;
  readonly retry_count: number;
};

// Notification batch for efficient delivery
export type NotificationBatch = {
  readonly id: string;
  readonly notifications: readonly NotificationPayload[];
  readonly created_at: Date;
  readonly priority: Priority;
  readonly channel_type: NotificationChannel["type"];
};

// Delivery queue item
export type QueueItem = {
  readonly id: string;
  readonly payload: NotificationPayload;
  readonly channel: NotificationChannel;
  readonly scheduled_at: Date;
  readonly retry_count: number;
  readonly max_retries: number;
};

// Retry configuration
export type RetryConfig = {
  readonly max_retries: number;
  readonly initial_delay_ms: number;
  readonly max_delay_ms: number;
  readonly backoff_multiplier: number;
  readonly jitter: boolean;
};

// Rate limiting configuration
export type RateLimitConfig = {
  readonly requests_per_minute: number;
  readonly burst_size: number;
  readonly window_size_ms: number;
};

// Notification service implementation
export class NotificationService {
  private readonly deliveryQueue: QueueItem[] = [];
  private readonly rateLimiters = new Map<string, RateLimitState>();
  private readonly retryConfig: RetryConfig = {
    max_retries: 3,
    initial_delay_ms: 1000,
    max_delay_ms: 30000,
    backoff_multiplier: 2,
    jitter: true,
  };

  constructor(
    private readonly subscriptions: readonly ChangeSubscription[],
    private readonly defaultRateLimit: RateLimitConfig = {
      requests_per_minute: 60,
      burst_size: 10,
      window_size_ms: 60000,
    }
  ) {}

  // Main notification dispatch method
  async dispatchNotifications(
    regulation: Regulation,
    changes?: readonly Change[],
  ): Promise<Result<readonly DeliveryResult[], Error>> {
    try {
      const notifications = this.createNotifications(regulation, changes);
      const deliveryResults = await this.deliverNotifications(notifications);
      
      return Ok(deliveryResults);
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // Create notifications based on subscriptions
  private createNotifications(
    regulation: Regulation,
    changes?: readonly Change[],
  ): readonly NotificationPayload[] {
    const notifications: NotificationPayload[] = [];
    const matchingSubscriptions = this.findMatchingSubscriptions(regulation, changes);

    for (const subscription of matchingSubscriptions) {
      const priority = this.determinePriority(regulation, changes, subscription);
      
      if (this.shouldNotify(priority, subscription)) {
        const payload: NotificationPayload = {
          id: crypto.randomUUID(),
          type: changes && changes.length > 0 ? "regulation_change" : "new_regulation",
          priority,
          timestamp: new Date(),
          regulation,
          changes: changes || undefined,
          metadata: {
            source: "wto-compliance-system",
            version: "1.0.0",
            retry_count: 0,
            correlation_id: crypto.randomUUID(),
            tags: this.generateTags(regulation, changes),
          },
        };

        notifications.push(payload);
      }
    }

    return notifications;
  }

  // Deliver notifications through configured channels
  private async deliverNotifications(
    notifications: readonly NotificationPayload[],
  ): Promise<readonly DeliveryResult[]> {
    const deliveryResults: DeliveryResult[] = [];
    const batches = this.groupNotificationsIntoBatches(notifications);

    for (const batch of batches) {
      const channelResults = await this.deliverBatch(batch);
      deliveryResults.push(...channelResults);
    }

    return deliveryResults;
  }

  // Deliver a batch of notifications
  private async deliverBatch(batch: NotificationBatch): Promise<readonly DeliveryResult[]> {
    const results: DeliveryResult[] = [];
    const channels = this.getChannelsForBatch(batch);

    for (const channel of channels) {
      const channelResults = await this.deliverToChannel(batch.notifications, channel);
      results.push(...channelResults);
    }

    return results;
  }

  // Deliver notifications to a specific channel
  private async deliverToChannel(
    notifications: readonly NotificationPayload[],
    channel: NotificationChannel,
  ): Promise<readonly DeliveryResult[]> {
    const results: DeliveryResult[] = [];

    for (const notification of notifications) {
      const result = await this.deliverSingleNotification(notification, channel);
      results.push(result);

      // Respect rate limits
      await this.enforceRateLimit(channel);
    }

    return results;
  }

  // Deliver a single notification with retry logic
  private async deliverSingleNotification(
    notification: NotificationPayload,
    channel: NotificationChannel,
    retryCount = 0,
  ): Promise<DeliveryResult> {
    const startTime = Date.now();

    try {
      const success = await this.sendToChannel(notification, channel);
      
      if (success) {
        return {
          channel,
          success: true,
          delivered_at: new Date(),
          response_time_ms: Date.now() - startTime,
          retry_count: retryCount,
        };
      } else {
        return this.handleDeliveryFailure(notification, channel, retryCount, startTime);
      }
    } catch (error) {
      return this.handleDeliveryError(notification, channel, error, retryCount, startTime);
    }
  }

  // Send notification to specific channel type
  private sendToChannel(
    notification: NotificationPayload,
    channel: NotificationChannel,
  ): Promise<boolean> {
    switch (channel.type) {
      case "email":
        return this.sendEmail(notification, channel);
      case "webhook":
        return this.sendWebhook(notification, channel);
      case "sms":
        return this.sendSMS(notification, channel);
      case "slack":
        return this.sendSlack(notification, channel);
      default:
        throw new Error(`Unsupported channel type: ${(channel as NotificationChannel).type}`);
    }
  }

  // Email delivery implementation
  private async sendEmail(
    notification: NotificationPayload,
    channel: Extract<NotificationChannel, { type: "email" }>,
  ): Promise<boolean> {
    const emailBody = this.formatEmailBody(notification);
    const subject = this.formatEmailSubject(notification);

    // Placeholder for email service integration
    // In production, integrate with services like SendGrid, AWS SES, etc.
    console.log(`Sending email to ${channel.addresses.join(", ")}`);
    console.log(`Subject: ${subject}`);
    console.log(`Body: ${emailBody}`);

    // Simulate email sending
    await this.delay(100);
    return true;
  }

  // Webhook delivery implementation
  private async sendWebhook(
    notification: NotificationPayload,
    channel: Extract<NotificationChannel, { type: "webhook" }>,
  ): Promise<boolean> {
    const headers = new Headers({
      "Content-Type": "application/json",
      "X-WTO-Compliance-Signature": await this.generateWebhookSignature(notification),
      "X-WTO-Compliance-Timestamp": notification.timestamp.toISOString(),
      "X-WTO-Compliance-Correlation-Id": notification.metadata.correlation_id,
      ...channel.headers,
    });

    const body = JSON.stringify({
      ...notification,
      timestamp: notification.timestamp.toISOString(),
    });

    try {
      const response = await fetch(channel.url, {
        method: "POST",
        headers,
        body,
      });

      return response.ok;
    } catch (error) {
      console.error(`Webhook delivery failed: ${error}`);
      return false;
    }
  }

  // SMS delivery implementation
  private async sendSMS(
    notification: NotificationPayload,
    channel: Extract<NotificationChannel, { type: "sms" }>,
  ): Promise<boolean> {
    const message = this.formatSMSMessage(notification);

    // Placeholder for SMS service integration
    // In production, integrate with services like Twilio, AWS SNS, etc.
    console.log(`Sending SMS to ${channel.numbers.join(", ")}`);
    console.log(`Message: ${message}`);

    // Simulate SMS sending
    await this.delay(200);
    return true;
  }

  // Slack delivery implementation
  private async sendSlack(
    notification: NotificationPayload,
    channel: Extract<NotificationChannel, { type: "slack" }>,
  ): Promise<boolean> {
    const slackPayload = this.formatSlackMessage(notification);

    try {
      const response = await fetch(channel.webhook_url, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          channel: channel.channel,
          ...slackPayload,
        }),
      });

      return response.ok;
    } catch (error) {
      console.error(`Slack delivery failed: ${error}`);
      return false;
    }
  }

  // Helper methods
  private findMatchingSubscriptions(
    regulation: Regulation,
    changes?: readonly Change[],
  ): readonly ChangeSubscription[] {
    return this.subscriptions.filter(subscription => {
      if (!subscription.active) return false;

      // Country filter
      if (subscription.country_codes.length > 0 &&
          !subscription.country_codes.includes(regulation.country_code)) {
        return false;
      }

      // Category filter
      if (subscription.regulation_categories.length > 0 &&
          !subscription.regulation_categories.includes(regulation.category)) {
        return false;
      }

      // HS code filter
      if (subscription.hs_codes.length > 0) {
        const hasMatchingHS = regulation.hs_codes.some(hsCode =>
          subscription.hs_codes.some(subHS => hsCode.startsWith(subHS))
        );
        if (!hasMatchingHS) return false;
      }

      // Priority filter
      if (changes && changes.length > 0) {
        const highestPriority = this.getHighestPriority(changes.map(c => c.priority));
        if (!this.isPriorityAboveThreshold(highestPriority, subscription.priority_threshold)) {
          return false;
        }
      }

      return true;
    });
  }

  private determinePriority(
    regulation: Regulation,
    changes?: readonly Change[],
    _subscription?: ChangeSubscription,
  ): Priority {
    if (changes && changes.length > 0) {
      return this.getHighestPriority(changes.map(c => c.priority));
    }

    // Determine priority based on regulation characteristics
    const impactScore = regulation.impact_assessment.urgency;
    if (impactScore >= 8) return "critical";
    if (impactScore >= 6) return "high";
    if (impactScore >= 4) return "medium";
    return "low";
  }

  private shouldNotify(priority: Priority, subscription: ChangeSubscription): boolean {
    return this.isPriorityAboveThreshold(priority, subscription.priority_threshold);
  }

  private isPriorityAboveThreshold(priority: Priority, threshold: Priority): boolean {
    const priorityLevels: Record<Priority, number> = {
      critical: 4,
      high: 3,
      medium: 2,
      low: 1,
    };

    return priorityLevels[priority] >= priorityLevels[threshold];
  }

  private getHighestPriority(priorities: readonly Priority[]): Priority {
    const priorityLevels: Record<Priority, number> = {
      critical: 4,
      high: 3,
      medium: 2,
      low: 1,
    };

    let highest: Priority = "low";
    let highestLevel = 0;

    for (const priority of priorities) {
      if (priorityLevels[priority] > highestLevel) {
        highest = priority;
        highestLevel = priorityLevels[priority];
      }
    }

    return highest;
  }

  private generateTags(regulation: Regulation, changes?: readonly Change[]): readonly string[] {
    const tags: string[] = [
      regulation.country_code,
      regulation.category,
      `priority:${this.determinePriority(regulation, changes)}`,
    ];

    if (changes && changes.length > 0) {
      tags.push(`changes:${changes.length}`);
      tags.push(...changes.map(c => `change:${c.change_type}`));
    }

    return tags;
  }

  private groupNotificationsIntoBatches(
    notifications: readonly NotificationPayload[],
  ): readonly NotificationBatch[] {
    const batches = new Map<string, NotificationPayload[]>();

    for (const notification of notifications) {
      const key = `${notification.priority}-${notification.type}`;
      const existing = batches.get(key) || [];
      existing.push(notification);
      batches.set(key, existing);
    }

    return Array.from(batches.entries()).map(([_key, notifications]) => ({
      id: crypto.randomUUID(),
      notifications,
      created_at: new Date(),
      priority: notifications[0].priority,
      channel_type: "webhook" as const, // Simplified for this example
    }));
  }

  private getChannelsForBatch(_batch: NotificationBatch): readonly NotificationChannel[] {
    // Get all channels from matching subscriptions
    const channels: NotificationChannel[] = [];
    
    for (const subscription of this.subscriptions) {
      if (subscription.active) {
        channels.push(...subscription.notification_config.channels);
      }
    }

    return channels;
  }

  private async enforceRateLimit(channel: NotificationChannel): Promise<void> {
    const channelId = this.getChannelId(channel);
    const rateLimit = this.getRateLimitForChannel(channel);
    
    await this.checkRateLimit(channelId, rateLimit);
  }

  private async checkRateLimit(channelId: string, config: RateLimitConfig): Promise<void> {
    const state = this.rateLimiters.get(channelId) || {
      requests: [],
      window_start: Date.now(),
    };

    const now = Date.now();
    const windowStart = now - config.window_size_ms;

    // Remove old requests outside the window
    state.requests = state.requests.filter(timestamp => timestamp > windowStart);

    if (state.requests.length >= config.requests_per_minute) {
      const oldestRequest = Math.min(...state.requests);
      const waitTime = oldestRequest + config.window_size_ms - now;
      
      if (waitTime > 0) {
        await this.delay(waitTime);
      }
    }

    state.requests.push(now);
    this.rateLimiters.set(channelId, state);
  }

  private handleDeliveryFailure(
    notification: NotificationPayload,
    channel: NotificationChannel,
    retryCount: number,
    startTime: number,
  ): DeliveryResult {
    if (retryCount < this.retryConfig.max_retries) {
      // Queue for retry
      this.queueForRetry(notification, channel, retryCount + 1);
    }

    return {
      channel,
      success: false,
      error: "Delivery failed",
      response_time_ms: Date.now() - startTime,
      retry_count: retryCount,
    };
  }

  private handleDeliveryError(
    notification: NotificationPayload,
    channel: NotificationChannel,
    error: unknown,
    retryCount: number,
    startTime: number,
  ): DeliveryResult {
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    if (retryCount < this.retryConfig.max_retries && this.isRetryableError(error)) {
      this.queueForRetry(notification, channel, retryCount + 1);
    }

    return {
      channel,
      success: false,
      error: errorMessage,
      response_time_ms: Date.now() - startTime,
      retry_count: retryCount,
    };
  }

  private queueForRetry(
    notification: NotificationPayload,
    channel: NotificationChannel,
    retryCount: number,
  ): void {
    const delay = this.calculateRetryDelay(retryCount);
    const scheduledAt = new Date(Date.now() + delay);

    const queueItem: QueueItem = {
      id: crypto.randomUUID(),
      payload: notification,
      channel,
      scheduled_at: scheduledAt,
      retry_count: retryCount,
      max_retries: this.retryConfig.max_retries,
    };

    this.deliveryQueue.push(queueItem);
  }

  private calculateRetryDelay(retryCount: number): number {
    let delay = this.retryConfig.initial_delay_ms * 
      Math.pow(this.retryConfig.backoff_multiplier, retryCount - 1);
    
    delay = Math.min(delay, this.retryConfig.max_delay_ms);
    
    if (this.retryConfig.jitter) {
      delay += Math.random() * 1000; // Add up to 1 second of jitter
    }
    
    return delay;
  }

  private isRetryableError(error: unknown): boolean {
    if (error instanceof Error) {
      // Network errors, timeouts, and 5xx server errors are retryable
      return error.message.includes("network") ||
             error.message.includes("timeout") ||
             error.message.includes("5");
    }
    return false;
  }

  // Formatting methods
  private formatEmailSubject(notification: NotificationPayload): string {
    const priority = notification.priority.toUpperCase();
    const type = notification.type.replace("_", " ").toUpperCase();
    return `[${priority}] ${type}: ${notification.regulation.title.en || "Regulation Update"}`;
  }

  private formatEmailBody(notification: NotificationPayload): string {
    const regulation = notification.regulation;
    const changes = notification.changes || [];
    
    let body = `
    <h2>Regulation Update</h2>
    <p><strong>Country:</strong> ${regulation.country_code}</p>
    <p><strong>Category:</strong> ${regulation.category}</p>
    <p><strong>Title:</strong> ${regulation.title.en || "N/A"}</p>
    <p><strong>Effective Date:</strong> ${regulation.timeline.effective_date.toDateString()}</p>
    `;

    if (changes.length > 0) {
      body += "<h3>Changes Detected:</h3><ul>";
      for (const change of changes) {
        body += `<li>${change.summary.en || "Change detected"}</li>`;
      }
      body += "</ul>";
    }

    return body;
  }

  private formatSMSMessage(notification: NotificationPayload): string {
    const regulation = notification.regulation;
    const priority = notification.priority.toUpperCase();
    return `[${priority}] ${regulation.country_code} regulation update: ${regulation.title.en?.substring(0, 100) || "Update"}`;
  }

  private formatSlackMessage(notification: NotificationPayload): Record<string, unknown> {
    const regulation = notification.regulation;
    const changes = notification.changes || [];
    
    return {
      text: `Regulation Update: ${regulation.title.en || "N/A"}`,
      attachments: [
        {
          color: this.getSlackColor(notification.priority),
          fields: [
            {
              title: "Country",
              value: regulation.country_code,
              short: true,
            },
            {
              title: "Category",
              value: regulation.category,
              short: true,
            },
            {
              title: "Priority",
              value: notification.priority.toUpperCase(),
              short: true,
            },
            {
              title: "Changes",
              value: changes.length.toString(),
              short: true,
            },
          ],
        },
      ],
    };
  }

  private getSlackColor(priority: Priority): string {
    const colors: Record<Priority, string> = {
      critical: "danger",
      high: "warning",
      medium: "good",
      low: "#36a64f",
    };
    return colors[priority];
  }

  private async generateWebhookSignature(notification: NotificationPayload): Promise<string> {
    const payload = JSON.stringify(notification);
    const encoder = new TextEncoder();
    const data = encoder.encode(payload);
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, "0")).join("");
  }

  private getChannelId(channel: NotificationChannel): string {
    switch (channel.type) {
      case "email":
        return `email:${channel.addresses.join(",")}`;
      case "webhook":
        return `webhook:${channel.url}`;
      case "sms":
        return `sms:${channel.numbers.join(",")}`;
      case "slack":
        return `slack:${channel.channel}`;
      default:
        return "unknown";
    }
  }

  private getRateLimitForChannel(channel: NotificationChannel): RateLimitConfig {
    // Channel-specific rate limits
    const channelLimits: Record<string, RateLimitConfig> = {
      email: { requests_per_minute: 100, burst_size: 20, window_size_ms: 60000 },
      webhook: { requests_per_minute: 300, burst_size: 50, window_size_ms: 60000 },
      sms: { requests_per_minute: 30, burst_size: 5, window_size_ms: 60000 },
      slack: { requests_per_minute: 60, burst_size: 10, window_size_ms: 60000 },
    };

    return channelLimits[channel.type] || this.defaultRateLimit;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Rate limiter state
type RateLimitState = {
  requests: number[];
  window_start: number;
};

// Factory function for creating notification service
export const createNotificationService = (
  subscriptions: readonly ChangeSubscription[],
): NotificationService => {
  return new NotificationService(subscriptions);
};