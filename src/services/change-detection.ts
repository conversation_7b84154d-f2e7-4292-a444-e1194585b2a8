// Change Detection Service - Intelligent Comparison Algorithms

import type { Result, Priority } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type { Regulation } from "../types/regulation.ts";
import type { 
  Change, 
  ChangeDetail, 
  ChangeDetectionResult,
  createChange,
  createChangeDetail 
} from "../types/change.ts";
import { calculateChangePriority, calculateChangeImpact } from "../types/change.ts";

// Similarity metrics for different data types
export type SimilarityMetric = {
  readonly field: string;
  readonly similarity_score: number; // 0-1, higher means more similar
  readonly change_detected: boolean;
  readonly confidence: number; // 0-1, confidence in detection
  readonly change_description?: string;
};

// Comparison context for algorithms
export type ComparisonContext = {
  readonly algorithm_version: string;
  readonly comparison_timestamp: Date;
  readonly fields_compared: readonly string[];
  readonly weights: Record<string, number>;
};

// Change detection configuration
export type DetectionConfig = {
  readonly similarity_threshold: number; // 0-1, below this triggers change detection
  readonly confidence_threshold: number; // 0-1, minimum confidence to report change
  readonly field_weights: Record<string, number>;
  readonly ignore_fields: readonly string[];
  readonly enable_semantic_analysis: boolean;
  readonly enable_numerical_analysis: boolean;
};

// Text similarity algorithms
type TextSimilarityAlgorithm = "levenshtein" | "cosine" | "jaccard" | "semantic";

// Numerical comparison result
type NumericalComparison = {
  readonly previous_value: number;
  readonly new_value: number;
  readonly absolute_change: number;
  readonly percentage_change: number;
  readonly significant: boolean;
};

// Change detection service implementation
export class ChangeDetectionService {
  private readonly defaultConfig: DetectionConfig = {
    similarity_threshold: 0.85,
    confidence_threshold: 0.7,
    field_weights: {
      title: 1.0,
      description: 0.8,
      timeline: 1.0,
      hs_codes: 0.9,
      impact_assessment: 0.8,
      category: 1.0,
    },
    ignore_fields: ["id", "version", "created_at", "updated_at"],
    enable_semantic_analysis: true,
    enable_numerical_analysis: true,
  };

  constructor(private readonly config: DetectionConfig = {}) {
    this.config = { ...this.defaultConfig, ...config };
  }

  // Main change detection method
  async detectChanges(
    previousRegulation: Regulation,
    currentRegulation: Regulation,
  ): Promise<Result<ChangeDetectionResult, Error>> {
    const startTime = Date.now();
    
    try {
      // Ensure we're comparing the same regulation
      if (previousRegulation.id !== currentRegulation.id) {
        return Err(new Error("Cannot compare regulations with different IDs"));
      }

      const similarities = await this.calculateFieldSimilarities(
        previousRegulation,
        currentRegulation,
      );

      const changes = this.identifySignificantChanges(
        similarities,
        previousRegulation,
        currentRegulation,
      );

      const overallSimilarity = this.calculateOverallSimilarity(similarities);
      const processingTime = Date.now() - startTime;

      const result: ChangeDetectionResult = {
        changes_detected: changes,
        similarity_score: overallSimilarity,
        detection_confidence: this.calculateDetectionConfidence(similarities),
        processing_time_ms: processingTime,
        algorithm_version: "1.0.0",
      };

      return Ok(result);
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // Batch change detection for multiple regulations
  async detectBatchChanges(
    regulationPairs: readonly [Regulation, Regulation][],
  ): Promise<Result<readonly ChangeDetectionResult[], Error>> {
    const results: ChangeDetectionResult[] = [];
    const errors: Error[] = [];

    for (const [previous, current] of regulationPairs) {
      const result = await this.detectChanges(previous, current);
      
      if (result.ok) {
        results.push(result.value);
      } else {
        errors.push(result.error);
      }
    }

    return errors.length === 0 
      ? Ok(results)
      : Err(new Error(`Batch detection failed: ${errors.map(e => e.message).join(', ')}`));
  }

  // Calculate similarity metrics for all comparable fields
  private async calculateFieldSimilarities(
    previous: Regulation,
    current: Regulation,
  ): Promise<readonly SimilarityMetric[]> {
    const similarities: SimilarityMetric[] = [];
    const fields = this.getComparableFields(previous, current);

    for (const field of fields) {
      if (this.config.ignore_fields.includes(field)) {
        continue;
      }

      const prevValue = this.getFieldValue(previous, field);
      const currValue = this.getFieldValue(current, field);
      
      const similarity = await this.calculateFieldSimilarity(
        field,
        prevValue,
        currValue,
      );
      
      similarities.push(similarity);
    }

    return similarities;
  }

  // Calculate similarity for a specific field
  private async calculateFieldSimilarity(
    field: string,
    previousValue: unknown,
    currentValue: unknown,
  ): Promise<SimilarityMetric> {
    // Handle null/undefined values
    if (previousValue == null && currentValue == null) {
      return {
        field,
        similarity_score: 1.0,
        change_detected: false,
        confidence: 1.0,
      };
    }

    if (previousValue == null || currentValue == null) {
      return {
        field,
        similarity_score: 0.0,
        change_detected: true,
        confidence: 1.0,
        change_description: `Field ${field} ${previousValue == null ? 'added' : 'removed'}`,
      };
    }

    // Type-specific comparison
    if (typeof previousValue === "string" && typeof currentValue === "string") {
      return this.compareTextFields(field, previousValue, currentValue);
    }

    if (typeof previousValue === "number" && typeof currentValue === "number") {
      return this.compareNumericalFields(field, previousValue, currentValue);
    }

    if (previousValue instanceof Date && currentValue instanceof Date) {
      return this.compareDateFields(field, previousValue, currentValue);
    }

    if (Array.isArray(previousValue) && Array.isArray(currentValue)) {
      return this.compareArrayFields(field, previousValue, currentValue);
    }

    if (typeof previousValue === "object" && typeof currentValue === "object") {
      return this.compareObjectFields(field, previousValue, currentValue);
    }

    // Fallback to simple equality
    const isEqual = JSON.stringify(previousValue) === JSON.stringify(currentValue);
    return {
      field,
      similarity_score: isEqual ? 1.0 : 0.0,
      change_detected: !isEqual,
      confidence: 1.0,
      change_description: isEqual ? undefined : `Field ${field} changed`,
    };
  }

  // Text comparison methods
  private compareTextFields(field: string, prev: string, curr: string): SimilarityMetric {
    const levenshteinSim = this.calculateLevenshteinSimilarity(prev, curr);
    const cosineSim = this.calculateCosineSimilarity(prev, curr);
    const jaccardSim = this.calculateJaccardSimilarity(prev, curr);
    
    // Weighted average of different similarity metrics
    const similarity = (levenshteinSim * 0.4 + cosineSim * 0.4 + jaccardSim * 0.2);
    const threshold = this.config.similarity_threshold;
    const changeDetected = similarity < threshold;
    
    return {
      field,
      similarity_score: similarity,
      change_detected: changeDetected,
      confidence: this.calculateTextConfidence(prev, curr, similarity),
      change_description: changeDetected 
        ? this.generateTextChangeDescription(prev, curr)
        : undefined,
    };
  }

  // Numerical comparison
  private compareNumericalFields(field: string, prev: number, curr: number): SimilarityMetric {
    const comparison = this.calculateNumericalComparison(prev, curr);
    const similarity = 1 - Math.min(1, Math.abs(comparison.percentage_change) / 100);
    
    return {
      field,
      similarity_score: similarity,
      change_detected: comparison.significant,
      confidence: 0.95, // High confidence for numerical changes
      change_description: comparison.significant 
        ? `${field} changed from ${prev} to ${curr} (${comparison.percentage_change.toFixed(1)}% change)`
        : undefined,
    };
  }

  // Date comparison
  private compareDateFields(field: string, prev: Date, curr: Date): SimilarityMetric {
    const timeDiffMs = Math.abs(curr.getTime() - prev.getTime());
    const daysDiff = timeDiffMs / (1000 * 60 * 60 * 24);
    
    // Consider dates the same if within 1 day (accounting for timezone issues)
    const similarity = daysDiff <= 1 ? 1.0 : 0.0;
    const changeDetected = daysDiff > 1;
    
    return {
      field,
      similarity_score: similarity,
      change_detected: changeDetected,
      confidence: 0.9,
      change_description: changeDetected 
        ? `${field} changed from ${prev.toISOString().split('T')[0]} to ${curr.toISOString().split('T')[0]}`
        : undefined,
    };
  }

  // Array comparison
  private compareArrayFields(field: string, prev: unknown[], curr: unknown[]): SimilarityMetric {
    const prevSet = new Set(prev.map(item => JSON.stringify(item)));
    const currSet = new Set(curr.map(item => JSON.stringify(item)));
    
    const intersection = new Set([...prevSet].filter(x => currSet.has(x)));
    const union = new Set([...prevSet, ...currSet]);
    
    const similarity = union.size > 0 ? intersection.size / union.size : 1.0;
    const changeDetected = similarity < this.config.similarity_threshold;
    
    return {
      field,
      similarity_score: similarity,
      change_detected: changeDetected,
      confidence: 0.85,
      change_description: changeDetected 
        ? this.generateArrayChangeDescription(field, prev, curr)
        : undefined,
    };
  }

  // Object comparison
  private compareObjectFields(field: string, prev: Record<string, unknown>, curr: Record<string, unknown>): SimilarityMetric {
    const prevKeys = Object.keys(prev);
    const currKeys = Object.keys(curr);
    const allKeys = [...new Set([...prevKeys, ...currKeys])];
    
    let similarFields = 0;
    let totalFields = allKeys.length;
    
    for (const key of allKeys) {
      if (JSON.stringify(prev[key]) === JSON.stringify(curr[key])) {
        similarFields++;
      }
    }
    
    const similarity = totalFields > 0 ? similarFields / totalFields : 1.0;
    const changeDetected = similarity < this.config.similarity_threshold;
    
    return {
      field,
      similarity_score: similarity,
      change_detected: changeDetected,
      confidence: 0.8,
      change_description: changeDetected 
        ? `${field} object modified (${similarFields}/${totalFields} fields unchanged)`
        : undefined,
    };
  }

  // Similarity calculation algorithms
  private calculateLevenshteinSimilarity(str1: string, str2: string): number {
    const distance = this.levenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);
    return maxLength > 0 ? 1 - (distance / maxLength) : 1.0;
  }

  private levenshteinDistance(str1: string, str2: string): number {
    const matrix: number[][] = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1,
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  }

  private calculateCosineSimilarity(str1: string, str2: string): number {
    const vector1 = this.getWordVector(str1);
    const vector2 = this.getWordVector(str2);
    
    const intersection = new Set([...Object.keys(vector1)].filter(x => Object.keys(vector2).includes(x)));
    
    let dotProduct = 0;
    for (const word of intersection) {
      dotProduct += vector1[word] * vector2[word];
    }
    
    const magnitude1 = Math.sqrt(Object.values(vector1).reduce((sum, freq) => sum + freq * freq, 0));
    const magnitude2 = Math.sqrt(Object.values(vector2).reduce((sum, freq) => sum + freq * freq, 0));
    
    return magnitude1 && magnitude2 ? dotProduct / (magnitude1 * magnitude2) : 0;
  }

  private calculateJaccardSimilarity(str1: string, str2: string): number {
    const words1 = new Set(this.tokenize(str1));
    const words2 = new Set(this.tokenize(str2));
    
    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);
    
    return union.size > 0 ? intersection.size / union.size : 1.0;
  }

  private calculateNumericalComparison(prev: number, curr: number): NumericalComparison {
    const absoluteChange = Math.abs(curr - prev);
    const percentageChange = prev !== 0 ? ((curr - prev) / prev) * 100 : 0;
    
    // Consider change significant if it's more than 5% or absolute change > 1
    const significant = Math.abs(percentageChange) > 5 || absoluteChange > 1;
    
    return {
      previous_value: prev,
      new_value: curr,
      absolute_change: absoluteChange,
      percentage_change: percentageChange,
      significant,
    };
  }

  // Helper methods
  private getWordVector(text: string): Record<string, number> {
    const words = this.tokenize(text.toLowerCase());
    const vector: Record<string, number> = {};
    
    for (const word of words) {
      vector[word] = (vector[word] || 0) + 1;
    }
    
    return vector;
  }

  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
  }

  private getComparableFields(obj1: Record<string, unknown>, obj2: Record<string, unknown>): string[] {
    return [...new Set([...Object.keys(obj1), ...Object.keys(obj2)])];
  }

  private getFieldValue(obj: Record<string, unknown>, field: string): unknown {
    return obj[field];
  }

  private calculateOverallSimilarity(similarities: readonly SimilarityMetric[]): number {
    if (similarities.length === 0) return 1.0;
    
    let weightedSum = 0;
    let totalWeight = 0;
    
    for (const sim of similarities) {
      const weight = this.config.field_weights[sim.field] || 0.5;
      weightedSum += sim.similarity_score * weight;
      totalWeight += weight;
    }
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  private calculateDetectionConfidence(similarities: readonly SimilarityMetric[]): number {
    if (similarities.length === 0) return 1.0;
    
    const avgConfidence = similarities.reduce((sum, sim) => sum + sim.confidence, 0) / similarities.length;
    return avgConfidence;
  }

  private identifySignificantChanges(
    similarities: readonly SimilarityMetric[],
    previous: Regulation,
    current: Regulation,
  ): Change[] {
    const significantChanges = similarities.filter(sim => 
      sim.change_detected && sim.confidence >= this.config.confidence_threshold
    );
    
    if (significantChanges.length === 0) {
      return [];
    }

    const changeDetails: ChangeDetail[] = significantChanges.map(sim => ({
      field: sim.field,
      previous_value: this.getFieldValue(previous, sim.field),
      new_value: this.getFieldValue(current, sim.field),
      change_description: sim.change_description || `${sim.field} modified`,
      confidence_score: sim.confidence,
    }));

    const change: Change = {
      id: crypto.randomUUID(),
      regulation_id: current.id,
      change_type: this.determineChangeType(changeDetails),
      priority: calculateChangePriority(changeDetails),
      impact_score: calculateChangeImpact(changeDetails),
      summary: { en: this.generateChangeSummary(changeDetails) },
      detailed_changes: changeDetails,
      affected_industries: this.identifyAffectedIndustries(changeDetails),
      implementation_date: current.timeline.effective_date,
      stakeholders: [],
      detected_at: new Date(),
      notification_sent: false,
    };

    return [change];
  }

  private calculateTextConfidence(prev: string, curr: string, similarity: number): number {
    const lengthDiff = Math.abs(prev.length - curr.length);
    const avgLength = (prev.length + curr.length) / 2;
    const lengthFactor = avgLength > 0 ? 1 - (lengthDiff / avgLength) : 1;
    
    return Math.min(1, similarity * 0.7 + lengthFactor * 0.3);
  }

  private generateTextChangeDescription(prev: string, curr: string): string {
    if (prev.length === 0) return "Text added";
    if (curr.length === 0) return "Text removed";
    
    const similarity = this.calculateLevenshteinSimilarity(prev, curr);
    if (similarity < 0.3) return "Text substantially modified";
    if (similarity < 0.7) return "Text partially modified";
    return "Text slightly modified";
  }

  private generateArrayChangeDescription(field: string, prev: unknown[], curr: unknown[]): string {
    const added = curr.length - prev.length;
    if (added > 0) return `${added} items added to ${field}`;
    if (added < 0) return `${Math.abs(added)} items removed from ${field}`;
    return `${field} items modified`;
  }

  private determineChangeType(details: readonly ChangeDetail[]): Change["change_type"] {
    // Simple heuristic to determine change type
    if (details.some(d => d.field === "timeline")) return "effective_date_change";
    if (details.some(d => d.field === "category")) return "scope_modification";
    if (details.some(d => d.field === "hs_codes")) return "scope_modification";
    return "amendment";
  }

  private generateChangeSummary(details: readonly ChangeDetail[]): string {
    const fieldCount = details.length;
    const mainFields = details.slice(0, 3).map(d => d.field).join(", ");
    
    if (fieldCount === 1) {
      return `${details[0].field} was modified`;
    } else if (fieldCount <= 3) {
      return `${mainFields} were modified`;
    } else {
      return `${mainFields} and ${fieldCount - 3} other fields were modified`;
    }
  }

  private identifyAffectedIndustries(details: readonly ChangeDetail[]): readonly string[] {
    // Extract industries from HS code changes
    const hsCodeChanges = details.filter(d => d.field === "hs_codes");
    if (hsCodeChanges.length === 0) return [];
    
    // Simple mapping of HS code prefixes to industries
    const industryMap: Record<string, string> = {
      "01": "Agriculture",
      "02": "Food Processing",
      "84": "Machinery",
      "85": "Electronics",
    };
    
    const industries = new Set<string>();
    for (const change of hsCodeChanges) {
      if (Array.isArray(change.new_value)) {
        for (const code of change.new_value as string[]) {
          const prefix = code.substring(0, 2);
          if (industryMap[prefix]) {
            industries.add(industryMap[prefix]);
          }
        }
      }
    }
    
    return Array.from(industries);
  }
}