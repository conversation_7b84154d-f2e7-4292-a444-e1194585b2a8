// Background job processor for monitoring and processing completed crawl jobs
import { match } from "ts-pattern";
import type { Result } from "../types/core.ts";
import { Ok, Err } from "../types/core.ts";
import type { CrawlingService, CrawlJob } from "./crawling.ts";
import type { RegulationService } from "../server/dependencies.ts";

// Job processor configuration
type JobProcessorConfig = {
  readonly checkIntervalMs: number;
  readonly maxRetries: number;
  readonly batchSize: number;
};

// Job processor service
export type JobProcessorService = {
  readonly start: () => void;
  readonly stop: () => void;
  readonly processAllCompletedJobs: () => Promise<Result<ProcessingResult>>;
  readonly isRunning: () => boolean;
};

type ProcessingResult = {
  readonly processed: number;
  readonly failed: number;
  readonly errors: readonly string[];
};

// Logger interface
type Logger = {
  readonly info: (message: string, meta?: Record<string, unknown>) => void;
  readonly error: (message: string, error?: Error, meta?: Record<string, unknown>) => void;
  readonly warn: (message: string, meta?: Record<string, unknown>) => void;
  readonly debug: (message: string, meta?: Record<string, unknown>) => void;
};

// Create job processor service
export const createJobProcessorService = (
  crawlingService: CrawlingService,
  regulationService: RegulationService,
  logger: Logger,
  config: JobProcessorConfig = {
    checkIntervalMs: 30000, // Check every 30 seconds
    maxRetries: 3,
    batchSize: 5,
  }
): JobProcessorService => {
  let intervalId: number | null = null;
  let isRunning = false;

  // In-memory job tracking (in production, use database)
  const jobTracker = new Map<string, { retries: number; lastAttempt: Date }>();

  const processCompletedJob = async (job: CrawlJob): Promise<Result<void>> => {
    try {
      logger.info(`Processing completed job`, { jobId: job.id, webSourceId: job.webSourceId });

      const processResult = await crawlingService.processCompletedJob(job.id);
      
      return match(processResult)
        .with({ success: true }, ({ data: result }) => {
          logger.info(`Job processed successfully`, {
            jobId: job.id,
            regulationsExtracted: result.regulations.length,
            changesDetected: result.changes.length,
          });

          // Store extracted regulations (in production, save to database)
          for (const regulation of result.regulations) {
            logger.debug(`Extracted regulation: ${regulation.title.en}`, {
              jobId: job.id,
              sourceUrl: regulation.document_metadata.source_url,
            });
          }

          // Store detected changes (in production, save to database)
          for (const change of result.changes) {
            logger.debug(`Detected change: ${change.type}`, {
              jobId: job.id,
              changeType: change.type,
            });
          }

          // Remove from retry tracker
          jobTracker.delete(job.id);
          
          return Ok(undefined);
        })
        .with({ success: false }, ({ error }) => {
          logger.error(`Failed to process job`, error, { jobId: job.id });
          return Err(error);
        })
        .exhaustive();
    } catch (error) {
      logger.error(`Unexpected error processing job`, error as Error, { jobId: job.id });
      return Err(error as Error);
    }
  };

  const checkAndProcessJobs = async (): Promise<void> => {
    try {
      logger.debug("Checking for completed jobs to process");

      // Get all running jobs (in production, query database)
      // For now, we'll simulate this by checking a few known job IDs
      // In a real implementation, you'd query your job storage

      const completedJobs: CrawlJob[] = [];
      
      // This is a placeholder - in production you'd query your job database
      // for jobs with status 'completed' that haven't been processed yet
      
      if (completedJobs.length === 0) {
        logger.debug("No completed jobs found to process");
        return;
      }

      logger.info(`Found ${completedJobs.length} completed jobs to process`);

      // Process jobs in batches
      for (let i = 0; i < completedJobs.length; i += config.batchSize) {
        const batch = completedJobs.slice(i, i + config.batchSize);
        
        await Promise.all(
          batch.map(async (job) => {
            const tracker = jobTracker.get(job.id) || { retries: 0, lastAttempt: new Date(0) };
            
            // Skip if we've exceeded max retries
            if (tracker.retries >= config.maxRetries) {
              logger.warn(`Job exceeded max retries, skipping`, { 
                jobId: job.id, 
                retries: tracker.retries 
              });
              return;
            }

            // Skip if we recently attempted this job (backoff)
            const timeSinceLastAttempt = Date.now() - tracker.lastAttempt.getTime();
            const backoffMs = Math.pow(2, tracker.retries) * 1000; // Exponential backoff
            
            if (timeSinceLastAttempt < backoffMs) {
              logger.debug(`Job in backoff period, skipping`, { 
                jobId: job.id, 
                backoffMs,
                timeSinceLastAttempt 
              });
              return;
            }

            // Update tracker
            jobTracker.set(job.id, {
              retries: tracker.retries + 1,
              lastAttempt: new Date(),
            });

            // Process the job
            const result = await processCompletedJob(job);
            
            if (!result.success) {
              logger.warn(`Job processing failed, will retry`, { 
                jobId: job.id, 
                retries: tracker.retries + 1,
                error: result.error.message 
              });
            }
          })
        );
      }
    } catch (error) {
      logger.error("Error in job processing cycle", error as Error);
    }
  };

  const service: JobProcessorService = {
    start: () => {
      if (isRunning) {
        logger.warn("Job processor is already running");
        return;
      }

      logger.info("Starting job processor", { 
        checkIntervalMs: config.checkIntervalMs,
        batchSize: config.batchSize,
        maxRetries: config.maxRetries 
      });

      isRunning = true;
      intervalId = setInterval(checkAndProcessJobs, config.checkIntervalMs);
    },

    stop: () => {
      if (!isRunning) {
        logger.warn("Job processor is not running");
        return;
      }

      logger.info("Stopping job processor");
      
      if (intervalId !== null) {
        clearInterval(intervalId);
        intervalId = null;
      }
      
      isRunning = false;
    },

    processAllCompletedJobs: async () => {
      logger.info("Manually processing all completed jobs");
      
      try {
        await checkAndProcessJobs();
        
        return Ok({
          processed: 0, // Would track actual numbers in production
          failed: 0,
          errors: [],
        });
      } catch (error) {
        logger.error("Failed to process completed jobs", error as Error);
        return Err(error as Error);
      }
    },

    isRunning: () => isRunning,
  };

  return service;
};
