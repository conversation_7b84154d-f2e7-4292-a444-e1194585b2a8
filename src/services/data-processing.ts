// Data Processing Pipeline - Validation, Classification, and Normalization

import type { Result, RegulationCategory, HSCode, CountryCode, MultiLangText } from "../types/core.ts";
import { Ok, Err, mapResult, flatMapResult } from "../types/core.ts";
import type { Regulation, RegulationInput } from "../types/regulation.ts";
import type { ScrapingResult } from "./data-collection.ts";
import { validateObject, regulationValidationRules } from "../validation/rules.ts";

// Processing pipeline stages
export type ProcessingStage = "validation" | "classification" | "normalization" | "enrichment";

// Processing result with stage information
export type ProcessingResult<T> = {
  readonly stage: ProcessingStage;
  readonly input: unknown;
  readonly output: Result<T, ProcessingError>;
  readonly processing_time_ms: number;
  readonly metadata: ProcessingMetadata;
};

// Processing error with detailed information
export type ProcessingError = {
  readonly stage: ProcessingStage;
  readonly error_type: "validation" | "classification" | "normalization" | "enrichment";
  readonly message: string;
  readonly details: unknown;
  readonly recoverable: boolean;
};

// Processing metadata
export type ProcessingMetadata = {
  readonly confidence_score: number; // 0-1
  readonly quality_score: number; // 0-1
  readonly language_detected: string;
  readonly source_reliability: number; // 0-1
  readonly processing_version: string;
};

// Classification result
export type ClassificationResult = {
  readonly category: RegulationCategory;
  readonly subcategory: string;
  readonly confidence: number;
  readonly alternative_categories: readonly { category: RegulationCategory; confidence: number }[];
  readonly detected_keywords: readonly string[];
};

// Normalization result
export type NormalizationResult = {
  readonly standardized_title: MultiLangText;
  readonly standardized_description: MultiLangText;
  readonly normalized_dates: {
    readonly effective_date: Date;
    readonly implementation_deadline?: Date;
  };
  readonly mapped_hs_codes: readonly HSCode[];
  readonly currency_conversions: Record<string, number>;
};

// Enrichment result
export type EnrichmentResult = {
  readonly related_regulations: readonly string[];
  readonly historical_context: string;
  readonly impact_assessment: {
    readonly affected_industries: readonly string[];
    readonly economic_impact: number;
    readonly compliance_complexity: number;
  };
  readonly stakeholder_mapping: readonly string[];
};

// Main data processing service
export class DataProcessingService {
  private readonly processingVersion = "1.0.0";
  
  // Process scraped data through the complete pipeline
  async processScrapingResults(
    results: readonly ScrapingResult[]
  ): Promise<Result<readonly Regulation[], ProcessingError>> {
    const processedRegulations: Regulation[] = [];
    const errors: ProcessingError[] = [];

    for (const result of results) {
      const processResult = await this.processSingleResult(result);
      
      if (processResult.ok) {
        processedRegulations.push(processResult.value);
      } else {
        errors.push(processResult.error);
      }
    }

    return errors.length === 0 
      ? Ok(processedRegulations)
      : Err({
          stage: "validation",
          error_type: "validation",
          message: `Processing failed for ${errors.length} items`,
          details: errors,
          recoverable: true,
        });
  }

  // Process a single scraping result through all stages
  private async processSingleResult(
    result: ScrapingResult
  ): Promise<Result<Regulation, ProcessingError>> {
    const startTime = Date.now();
    
    // Stage 1: Validation
    const validationResult = await this.validateRawData(result);
    if (!validationResult.ok) return validationResult;

    // Stage 2: Classification  
    const classificationResult = await this.classifyRegulation(validationResult.value);
    if (!classificationResult.ok) return Err(classificationResult.error);

    // Stage 3: Normalization
    const normalizationResult = await this.normalizeData(
      validationResult.value, 
      classificationResult.value
    );
    if (!normalizationResult.ok) return Err(normalizationResult.error);

    // Stage 4: Enrichment
    const enrichmentResult = await this.enrichData(normalizationResult.value);
    if (!enrichmentResult.ok) return Err(enrichmentResult.error);

    const totalProcessingTime = Date.now() - startTime;
    
    return Ok({
      ...enrichmentResult.value,
      document_metadata: {
        ...result.metadata,
        source_url: result.url,
        document_hash: await this.calculateContentHash(result.content),
        extracted_at: result.extracted_at,
      },
    });
  }

  // Stage 1: Data validation
  private async validateRawData(
    result: ScrapingResult
  ): Promise<Result<RegulationInput, ProcessingError>> {
    try {
      const extracted = await this.extractRegulationData(result);
      
      const validationResult = validateObject(extracted, regulationValidationRules);
      
      if (validationResult.some) {
        return Err({
          stage: "validation",
          error_type: "validation",
          message: "Validation failed",
          details: validationResult.value,
          recoverable: false,
        });
      }

      return Ok(extracted);
    } catch (error) {
      return Err({
        stage: "validation",
        error_type: "validation",
        message: error instanceof Error ? error.message : String(error),
        details: error,
        recoverable: false,
      });
    }
  }

  // Stage 2: Classification
  private async classifyRegulation(
    input: RegulationInput
  ): Promise<Result<ClassificationResult, ProcessingError>> {
    try {
      const title = Object.values(input.title)[0] || "";
      const description = Object.values(input.description)[0] || "";
      const content = `${title} ${description}`.toLowerCase();

      const classification = this.performClassification(content);
      
      return Ok(classification);
    } catch (error) {
      return Err({
        stage: "classification",
        error_type: "classification",
        message: error instanceof Error ? error.message : String(error),
        details: error,
        recoverable: true,
      });
    }
  }

  // Stage 3: Normalization
  private async normalizeData(
    input: RegulationInput,
    classification: ClassificationResult
  ): Promise<Result<RegulationInput, ProcessingError>> {
    try {
      const normalized: RegulationInput = {
        ...input,
        category: classification.category,
        subcategory: classification.subcategory,
        title: await this.normalizeMultiLangText(input.title),
        description: await this.normalizeMultiLangText(input.description),
        hs_codes: this.normalizeHSCodes(input.hs_codes),
        timeline: {
          ...input.timeline,
          effective_date: this.normalizeDate(input.timeline.effective_date),
          implementation_deadline: input.timeline.implementation_deadline 
            ? this.normalizeDate(input.timeline.implementation_deadline)
            : undefined,
        },
      };

      return Ok(normalized);
    } catch (error) {
      return Err({
        stage: "normalization",
        error_type: "normalization",
        message: error instanceof Error ? error.message : String(error),
        details: error,
        recoverable: true,
      });
    }
  }

  // Stage 4: Enrichment
  private async enrichData(
    input: RegulationInput
  ): Promise<Result<Regulation, ProcessingError>> {
    try {
      const enrichment = await this.performEnrichment(input);
      
      const regulation: Regulation = {
        id: crypto.randomUUID(),
        ...input,
        related_regulations: enrichment.related_regulations,
        impact_assessment: {
          economic: enrichment.impact_assessment.economic_impact,
          operational: enrichment.impact_assessment.compliance_complexity,
          compliance: this.calculateComplianceScore(input),
          urgency: this.calculateUrgencyScore(input.timeline),
        },
        version: 1,
        created_at: new Date(),
        updated_at: new Date(),
      };

      return Ok(regulation);
    } catch (error) {
      return Err({
        stage: "enrichment",
        error_type: "enrichment",
        message: error instanceof Error ? error.message : String(error),
        details: error,
        recoverable: true,
      });
    }
  }

  // Extract regulation data from scraping result
  private async extractRegulationData(result: ScrapingResult): Promise<RegulationInput> {
    const content = result.content;
    const language = result.metadata.language || "en";
    
    // Extract key information using patterns and heuristics
    const title = this.extractTitle(content) || "Untitled Regulation";
    const description = this.extractDescription(content) || "";
    const effectiveDate = this.extractEffectiveDate(content) || new Date();
    
    return {
      country_code: this.extractCountryCode(result.source_id),
      source_agency: result.source_id,
      title: { [language]: title },
      description: { [language]: description },
      category: "technical_regulation", // Default, will be classified later
      subcategory: "",
      hs_codes: this.extractHSCodes(content),
      timeline: {
        effective_date: effectiveDate,
        implementation_deadline: this.extractImplementationDate(content),
      },
      impact_assessment: {
        economic: 5,
        operational: 5,
        compliance: 5,
        urgency: 5,
      },
      related_regulations: [],
      original_language: language,
      document_metadata: {
        source_url: result.url,
        document_hash: "",
        content_type: result.metadata.content_type,
        file_size: result.metadata.content_length,
        extracted_at: result.extracted_at,
      },
    };
  }

  // Classification implementation using keyword matching and ML patterns
  private performClassification(content: string): ClassificationResult {
    const categories: Array<{ category: RegulationCategory; keywords: string[]; weight: number }> = [
      { category: "tariff", keywords: ["tariff", "duty", "customs", "import tax"], weight: 1.0 },
      { category: "non_tariff_barrier", keywords: ["quota", "restriction", "embargo", "ban"], weight: 0.9 },
      { category: "technical_regulation", keywords: ["standard", "specification", "requirement"], weight: 0.8 },
      { category: "sanitary_phytosanitary", keywords: ["health", "safety", "sanitary", "food"], weight: 0.9 },
      { category: "trade_remedies", keywords: ["anti-dumping", "safeguard", "countervailing"], weight: 1.0 },
      { category: "services_regulation", keywords: ["service", "banking", "insurance", "telecom"], weight: 0.7 },
      { category: "intellectual_property", keywords: ["patent", "trademark", "copyright", "ip"], weight: 0.8 },
      { category: "government_procurement", keywords: ["procurement", "tender", "bidding"], weight: 0.7 },
      { category: "customs_procedure", keywords: ["customs", "procedure", "clearance", "declaration"], weight: 0.8 },
      { category: "trade_facilitation", keywords: ["facilitation", "streamline", "efficiency"], weight: 0.6 },
    ];

    const scores = categories.map(cat => {
      const matchCount = cat.keywords.reduce((count, keyword) => 
        count + (content.includes(keyword) ? 1 : 0), 0
      );
      return {
        category: cat.category,
        confidence: (matchCount / cat.keywords.length) * cat.weight,
      };
    });

    scores.sort((a, b) => b.confidence - a.confidence);
    
    const topCategory = scores[0];
    const alternatives = scores.slice(1, 4);
    
    return {
      category: topCategory.category,
      subcategory: this.determineSubcategory(topCategory.category, content),
      confidence: topCategory.confidence,
      alternative_categories: alternatives,
      detected_keywords: this.extractKeywords(content),
    };
  }

  // Normalization helper methods
  private async normalizeMultiLangText(text: MultiLangText): Promise<MultiLangText> {
    // Clean and standardize text formatting
    const normalized: Record<string, string> = {};
    
    for (const [lang, content] of Object.entries(text)) {
      normalized[lang] = content
        .replace(/\s+/g, " ")
        .replace(/[""]/g, '"')
        .replace(/['']/g, "'")
        .trim();
    }
    
    return normalized;
  }

  private normalizeHSCodes(hsCodes: readonly HSCode[]): readonly HSCode[] {
    return hsCodes
      .map(code => code.replace(/\D/g, "")) // Remove non-digits
      .filter(code => code.length >= 4 && code.length <= 10)
      .filter((code, index, arr) => arr.indexOf(code) === index); // Remove duplicates
  }

  private normalizeDate(date: Date): Date {
    // Ensure date is valid and in reasonable range
    const now = new Date();
    const maxFutureYears = 10;
    const maxPastYears = 50;
    
    if (isNaN(date.getTime())) {
      return now;
    }
    
    const yearDiff = date.getFullYear() - now.getFullYear();
    
    if (yearDiff > maxFutureYears) {
      date.setFullYear(now.getFullYear() + maxFutureYears);
    } else if (yearDiff < -maxPastYears) {
      date.setFullYear(now.getFullYear() - maxPastYears);
    }
    
    return date;
  }

  // Enrichment implementation
  private async performEnrichment(input: RegulationInput): Promise<EnrichmentResult> {
    return {
      related_regulations: await this.findRelatedRegulations(input),
      historical_context: this.generateHistoricalContext(input),
      impact_assessment: {
        affected_industries: this.identifyAffectedIndustries(input),
        economic_impact: this.calculateEconomicImpact(input),
        compliance_complexity: this.calculateComplianceComplexity(input),
      },
      stakeholder_mapping: this.identifyStakeholders(input),
    };
  }

  // Utility methods for data extraction
  private extractTitle(content: string): string | null {
    const patterns = [
      /<title[^>]*>([^<]+)<\/title>/i,
      /<h1[^>]*>([^<]+)<\/h1>/i,
      /^(.+?)(?:\n|\r\n)/,
    ];
    
    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }
    
    return null;
  }

  private extractDescription(content: string): string {
    const cleaned = content
      .replace(/<[^>]+>/g, " ")
      .replace(/\s+/g, " ")
      .trim();
    
    return cleaned.length > 500 ? cleaned.substring(0, 500) + "..." : cleaned;
  }

  private extractEffectiveDate(content: string): Date | null {
    const datePatterns = [
      /effective\s+(?:date|from)?\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i,
      /(\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})/,
      /(\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s+\d{4})/i,
    ];
    
    for (const pattern of datePatterns) {
      const match = content.match(pattern);
      if (match) {
        const date = new Date(match[1]);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }
    
    return null;
  }

  private extractImplementationDate(content: string): Date | undefined {
    const patterns = [
      /implementation\s+(?:date|deadline)?\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i,
      /deadline\s*:?\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i,
    ];
    
    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match) {
        const date = new Date(match[1]);
        if (!isNaN(date.getTime())) {
          return date;
        }
      }
    }
    
    return undefined;
  }

  private extractHSCodes(content: string): readonly HSCode[] {
    const pattern = /(?:HS|Harmonized System)\s*:?\s*(\d{4,10})/gi;
    const codes: string[] = [];
    let match;
    
    while ((match = pattern.exec(content)) !== null) {
      codes.push(match[1]);
    }
    
    return codes;
  }

  private extractCountryCode(sourceId: string): CountryCode {
    const mapping: Record<string, CountryCode> = {
      "us-trade-gov": "US",
      "eu-trade": "EU",
      "jp-jetro": "JP",
      "cn-mofcom": "CN",
      "uk-trade": "GB",
    };
    
    return mapping[sourceId] || "UN";
  }

  private async calculateContentHash(content: string): Promise<string> {
    const encoder = new TextEncoder();
    const data = encoder.encode(content);
    const hashBuffer = await crypto.subtle.digest("SHA-256", data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, "0")).join("");
  }

  // Scoring and analysis methods
  private calculateComplianceScore(input: RegulationInput): number {
    const complexity = input.hs_codes.length * 0.5;
    const urgency = this.calculateUrgencyScore(input.timeline);
    return Math.min(10, (complexity + urgency) * 0.5);
  }

  private calculateUrgencyScore(timeline: RegulationInput["timeline"]): number {
    const now = new Date();
    const daysUntilEffective = Math.floor(
      (timeline.effective_date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysUntilEffective < 30) return 10;
    if (daysUntilEffective < 90) return 8;
    if (daysUntilEffective < 180) return 6;
    if (daysUntilEffective < 365) return 4;
    return 2;
  }

  private determineSubcategory(category: RegulationCategory, content: string): string {
    // Simple subcategory determination based on content analysis
    const subcategories: Record<RegulationCategory, string[]> = {
      tariff: ["ad valorem", "specific", "compound", "preferential"],
      non_tariff_barrier: ["quantitative", "licensing", "embargo", "voluntary restraint"],
      technical_regulation: ["mandatory", "voluntary", "conformity assessment", "labeling"],
      sanitary_phytosanitary: ["food safety", "animal health", "plant health", "risk assessment"],
      trade_remedies: ["anti-dumping", "countervailing", "safeguard", "surveillance"],
      services_regulation: ["financial", "telecommunications", "professional", "transport"],
      intellectual_property: ["patent", "trademark", "copyright", "industrial design"],
      government_procurement: ["goods", "services", "construction", "concessions"],
      customs_procedure: ["classification", "valuation", "documentation", "inspection"],
      trade_facilitation: ["single window", "risk management", "authorized trader", "transit"],
    };
    
    const candidates = subcategories[category] || [];
    for (const candidate of candidates) {
      if (content.toLowerCase().includes(candidate)) {
        return candidate;
      }
    }
    
    return candidates[0] || "general";
  }

  private extractKeywords(content: string): readonly string[] {
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, " ")
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    const wordCounts = new Map<string, number>();
    for (const word of words) {
      wordCounts.set(word, (wordCounts.get(word) || 0) + 1);
    }
    
    return Array.from(wordCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  private async findRelatedRegulations(input: RegulationInput): Promise<readonly string[]> {
    // Placeholder for finding related regulations
    // In production, this would query a database or index
    return [];
  }

  private generateHistoricalContext(input: RegulationInput): string {
    return `Regulation from ${input.country_code} in category ${input.category}`;
  }

  private identifyAffectedIndustries(input: RegulationInput): readonly string[] {
    // Map HS codes to industries
    const industryMapping: Record<string, string[]> = {
      "01": ["agriculture", "livestock"],
      "02": ["meat", "food processing"],
      "84": ["machinery", "manufacturing"],
      "85": ["electronics", "technology"],
    };
    
    const industries = new Set<string>();
    for (const hsCode of input.hs_codes) {
      const prefix = hsCode.substring(0, 2);
      const mapped = industryMapping[prefix];
      if (mapped) {
        mapped.forEach(industry => industries.add(industry));
      }
    }
    
    return Array.from(industries);
  }

  private calculateEconomicImpact(input: RegulationInput): number {
    // Simple economic impact calculation
    const hsCodeCount = input.hs_codes.length;
    const categoryWeights: Record<RegulationCategory, number> = {
      tariff: 1.0,
      non_tariff_barrier: 0.8,
      technical_regulation: 0.6,
      sanitary_phytosanitary: 0.7,
      trade_remedies: 0.9,
      services_regulation: 0.5,
      intellectual_property: 0.4,
      government_procurement: 0.3,
      customs_procedure: 0.2,
      trade_facilitation: 0.1,
    };
    
    const weight = categoryWeights[input.category] || 0.5;
    return Math.min(10, hsCodeCount * weight * 2);
  }

  private calculateComplianceComplexity(input: RegulationInput): number {
    const titleLength = Object.values(input.title)[0]?.length || 0;
    const descriptionLength = Object.values(input.description)[0]?.length || 0;
    const complexity = (titleLength + descriptionLength) / 200;
    return Math.min(10, Math.max(1, complexity));
  }

  private identifyStakeholders(input: RegulationInput): readonly string[] {
    const stakeholders = ["importers", "exporters", "customs_authorities"];
    
    // Add category-specific stakeholders
    const categoryStakeholders: Record<RegulationCategory, string[]> = {
      tariff: ["traders", "customs_brokers"],
      technical_regulation: ["manufacturers", "testing_laboratories"],
      sanitary_phytosanitary: ["food_producers", "health_authorities"],
      services_regulation: ["service_providers", "regulatory_bodies"],
      intellectual_property: ["rights_holders", "patent_offices"],
      government_procurement: ["suppliers", "procurement_agencies"],
      customs_procedure: ["logistics_providers", "freight_forwarders"],
      trade_facilitation: ["trade_facilitation_committees"],
      non_tariff_barrier: ["industry_associations"],
      trade_remedies: ["domestic_producers", "trade_remedy_authorities"],
    };
    
    const additional = categoryStakeholders[input.category] || [];
    return [...stakeholders, ...additional];
  }
}