// Data Collection Service - Web Scraping and API Integration

import type { Result, CountryCode } from "../types/core.ts";
import { Ok, Err, isOk, collectResults } from "../types/core.ts";

// Data source configuration
export type DataSource = {
  readonly id: string;
  readonly name: string;
  readonly country_code: CountryCode;
  readonly source_type: "web_scraping" | "api" | "document_feed";
  readonly base_url: string;
  readonly rate_limit_ms: number;
  readonly authentication?: AuthConfig;
  readonly extraction_config: ExtractionConfig;
  readonly active: boolean;
};

// Authentication configuration
export type AuthConfig =
  | { readonly type: "api_key"; readonly key: string; readonly header_name: string }
  | { readonly type: "bearer_token"; readonly token: string }
  | { readonly type: "basic_auth"; readonly username: string; readonly password: string }
  | { readonly type: "oauth2"; readonly client_id: string; readonly client_secret: string };

// Content extraction configuration
export type ExtractionConfig = {
  readonly selectors: Record<string, string>;
  readonly patterns: Record<string, RegExp>;
  readonly document_types: readonly string[];
  readonly language_detection: boolean;
  readonly clean_html: boolean;
};

// Scraping result
export type ScrapingResult = {
  readonly source_id: string;
  readonly url: string;
  readonly content: string;
  readonly metadata: ScrapingMetadata;
  readonly extracted_at: Date;
  readonly success: boolean;
};

// Scraping metadata
export type ScrapingMetadata = {
  readonly title?: string;
  readonly description?: string;
  readonly language?: string;
  readonly last_modified?: Date;
  readonly content_type: string;
  readonly content_length: number;
  readonly response_time_ms: number;
};

// Rate limiter state
type RateLimiterState = {
  readonly requests: readonly Date[];
  readonly last_request?: Date;
};

// Data collection service implementation
export class DataCollectionService {
  private readonly rateLimiters = new Map<string, RateLimiterState>();
  private readonly userAgent = "WTO-Compliance-Monitor/1.0";

  constructor(private readonly dataSources: readonly DataSource[]) {}

  // Main collection method
  async collectFromAllSources(): Promise<Result<readonly ScrapingResult[], Error>> {
    const activeSources = this.dataSources.filter(s => s.active);
    
    const collectionPromises = activeSources.map(async (source, index) => {
      // Stagger requests to respect rate limits
      await this.delay(source.rate_limit_ms * index);
      return this.collectFromSource(source);
    });

    const results = await Promise.all(collectionPromises);
    const collectedResults = collectResults(results);
    
    if (!isOk(collectedResults)) {
      const errors = collectedResults.error;
      return Err(new Error(`Collection failed for ${errors.length} sources: ${errors.map(e => e.message).join(', ')}`));
    }
    
    const allResults = collectedResults.data.flat();
    return Ok(allResults);
  }

  // Collect from a specific data source
  async collectFromSource(source: DataSource): Promise<Result<readonly ScrapingResult[], Error>> {
    try {
      await this.checkRateLimit(source.id, source.rate_limit_ms);
      
      switch (source.source_type) {
        case "web_scraping":
          return await this.scrapeWebContent(source);
        case "api":
          return await this.collectFromAPI(source);
        case "document_feed":
          return await this.collectFromDocumentFeed(source);
        default:
          return Err(new Error(`Unsupported source type: ${source.source_type}`));
      }
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // Web scraping implementation
  private async scrapeWebContent(source: DataSource): Promise<Result<readonly ScrapingResult[], Error>> {
    const startTime = Date.now();
    
    try {
      const response = await this.fetchWithAuth(source.base_url, source.authentication);
      
      if (!response.ok) {
        return Err(new Error(`HTTP ${response.status}: ${response.statusText}`));
      }

      const content = await response.text();
      const responseTime = Date.now() - startTime;
      
      const extractedContent = this.extractContent(content, source.extraction_config);
      
      const metadata: ScrapingMetadata = {
        title: this.extractTitle(content) || undefined,
        description: this.extractDescription(content) || undefined,
        language: source.extraction_config.language_detection 
          ? this.detectLanguage(content) 
          : undefined,
        content_type: response.headers.get("content-type") || "text/html",
        content_length: content.length,
        response_time_ms: responseTime,
        last_modified: this.parseLastModified(response.headers.get("last-modified")),
      };

      const result: ScrapingResult = {
        source_id: source.id,
        url: source.base_url,
        content: extractedContent,
        metadata,
        extracted_at: new Date(),
        success: true,
      };

      return Ok([result]);
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // API data collection
  private async collectFromAPI(source: DataSource): Promise<Result<readonly ScrapingResult[], Error>> {
    const startTime = Date.now();
    
    try {
      const response = await this.fetchWithAuth(source.base_url, source.authentication);
      
      if (!response.ok) {
        return Err(new Error(`API Error ${response.status}: ${response.statusText}`));
      }

      const data = await response.json();
      const responseTime = Date.now() - startTime;
      
      // Convert API response to standardized format
      const results = this.processAPIResponse(data, source, responseTime);
      
      return Ok(results);
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // Document feed collection
  private async collectFromDocumentFeed(source: DataSource): Promise<Result<readonly ScrapingResult[], Error>> {
    // Implementation for RSS/Atom feeds or document repositories
    try {
      const response = await this.fetchWithAuth(source.base_url, source.authentication);
      const feedContent = await response.text();
      
      // Parse feed and extract document links
      const documentUrls = this.extractDocumentUrls(feedContent, source.extraction_config);
      const results: ScrapingResult[] = [];
      
      for (const url of documentUrls) {
        const docResult = await this.fetchDocument(url, source);
        if (docResult.ok) {
          results.push(docResult.value);
        }
      }
      
      return Ok(results);
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }

  // Fetch with authentication
  private fetchWithAuth(url: string, auth?: AuthConfig): Promise<Response> {
    const headers = new Headers({
      "User-Agent": this.userAgent,
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
      "Accept-Language": "en-US,en;q=0.5",
      "Accept-Encoding": "gzip, deflate",
      "Connection": "keep-alive",
    });

    if (auth) {
      switch (auth.type) {
        case "api_key":
          headers.set(auth.header_name, auth.key);
          break;
        case "bearer_token": {
          headers.set("Authorization", `Bearer ${auth.token}`);
          break;
        }
        case "basic_auth": {
          const credentials = btoa(`${auth.username}:${auth.password}`);
          headers.set("Authorization", `Basic ${credentials}`);
          break;
        }
        case "oauth2": {
          // OAuth2 implementation would require token management
          throw new Error("OAuth2 authentication not yet implemented");
        }
      }
    }

    return fetch(url, { headers });
  }

  // Content extraction using selectors and patterns
  private extractContent(html: string, config: ExtractionConfig): string {
    let content = html;
    
    if (config.clean_html) {
      // Remove scripts, styles, and other unwanted elements
      content = content
        .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, "")
        .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, "")
        .replace(/<[^>]+>/g, " ")
        .replace(/\s+/g, " ")
        .trim();
    }

    // Apply extraction patterns
    for (const [_key, pattern] of Object.entries(config.patterns)) {
      const matches = content.match(pattern);
      if (matches) {
        content = matches[1] || matches[0];
      }
    }

    return content;
  }

  // Rate limiting implementation
  private async checkRateLimit(sourceId: string, rateLimitMs: number): Promise<void> {
    const state = this.rateLimiters.get(sourceId) || { requests: [] };
    const now = new Date();
    
    if (state.last_request) {
      const timeSinceLastRequest = now.getTime() - state.last_request.getTime();
      if (timeSinceLastRequest < rateLimitMs) {
        await this.delay(rateLimitMs - timeSinceLastRequest);
      }
    }
    
    this.rateLimiters.set(sourceId, {
      requests: [...state.requests, now],
      last_request: now,
    });
  }

  // Utility methods
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private extractTitle(html: string): string | undefined {
    const match = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    return match?.[1]?.trim();
  }

  private extractDescription(html: string): string | undefined {
    const match = html.match(/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"']+)["\'][^>]*>/i);
    return match?.[1]?.trim();
  }

  private detectLanguage(content: string): string {
    // Simple language detection - in production, use a proper library
    if (/[àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿ]/i.test(content)) return "fr";
    if (/[äöüß]/i.test(content)) return "de";
    if (/[¿¡ñáéíóúü]/i.test(content)) return "es";
    return "en";
  }

  private parseLastModified(lastModified: string | null): Date | undefined {
    return lastModified ? new Date(lastModified) : undefined;
  }

  private processAPIResponse(data: unknown, source: DataSource, responseTime: number): ScrapingResult[] {
    // Convert API response to ScrapingResult format
    const results: ScrapingResult[] = [];
    
    if (Array.isArray(data)) {
      for (const item of data) {
        results.push({
          source_id: source.id,
          url: source.base_url,
          content: JSON.stringify(item),
          metadata: {
            content_type: "application/json",
            content_length: JSON.stringify(item).length,
            response_time_ms: responseTime,
          },
          extracted_at: new Date(),
          success: true,
        });
      }
    } else {
      results.push({
        source_id: source.id,
        url: source.base_url,
        content: JSON.stringify(data),
        metadata: {
          content_type: "application/json",
          content_length: JSON.stringify(data).length,
          response_time_ms: responseTime,
        },
        extracted_at: new Date(),
        success: true,
      });
    }
    
    return results;
  }

  private extractDocumentUrls(feedContent: string, _config: ExtractionConfig): string[] {
    const urls: string[] = [];
    
    // Extract URLs from RSS/Atom feeds or HTML pages
    const urlPatterns = [
      /<link[^>]*href=["\']([^"']+\.pdf)["\'][^>]*>/gi,
      /<a[^>]*href=["\']([^"']+\.pdf)["\'][^>]*>/gi,
      /<url>([^<]+)<\/url>/gi,
    ];
    
    for (const pattern of urlPatterns) {
      let match;
      while ((match = pattern.exec(feedContent)) !== null) {
        urls.push(match[1]);
      }
    }
    
    return [...new Set(urls)]; // Remove duplicates
  }

  private async fetchDocument(url: string, source: DataSource): Promise<Result<ScrapingResult, Error>> {
    try {
      const response = await this.fetchWithAuth(url, source.authentication);
      
      if (!response.ok) {
        return Err(new Error(`Failed to fetch document: ${response.status}`));
      }
      
      const content = await response.text();
      
      const result: ScrapingResult = {
        source_id: source.id,
        url,
        content,
        metadata: {
          content_type: response.headers.get("content-type") || "application/octet-stream",
          content_length: content.length,
          response_time_ms: 0,
        },
        extracted_at: new Date(),
        success: true,
      };
      
      return Ok(result);
    } catch (error) {
      return Err(error instanceof Error ? error : new Error(String(error)));
    }
  }
}

// Factory function for creating data sources
export const createDataSource = (
  id: string,
  name: string,
  countryCode: CountryCode,
  sourceType: DataSource["source_type"],
  baseUrl: string,
  rateLimitMs = 1000,
): DataSource => ({
  id,
  name,
  country_code: countryCode,
  source_type: sourceType,
  base_url: baseUrl,
  rate_limit_ms: rateLimitMs,
  extraction_config: {
    selectors: {},
    patterns: {},
    document_types: ["pdf", "html", "xml"],
    language_detection: true,
    clean_html: true,
  },
  active: true,
});

// Predefined data sources for major countries
export const createWTODataSources = (): readonly DataSource[] => [
  createDataSource("us-trade-gov", "US Trade.gov", "US", "web_scraping", "https://www.trade.gov/trade-policy"),
  createDataSource("eu-trade", "EU Trade Policy", "EU", "api", "https://policy.trade.ec.europa.eu/api/regulations"),
  createDataSource("jp-jetro", "Japan JETRO", "JP", "web_scraping", "https://www.jetro.go.jp/en/trade/"),
  createDataSource("cn-mofcom", "China MOFCOM", "CN", "web_scraping", "http://english.mofcom.gov.cn/"),
  createDataSource("uk-trade", "UK Trade Bulletins", "GB", "document_feed", "https://www.gov.uk/trade-bulletins.rss"),
];