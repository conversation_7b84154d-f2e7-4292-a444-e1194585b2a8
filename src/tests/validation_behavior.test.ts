// Behavior-driven tests for validation functions
import { assertEquals, assertExists } from "@std/assert";
import {
  validateRegulation,
  validateRegulationInput,
  validateChange,
  validateSubscription,
  validateRegulationFilters,
  validateChangeFilters,
  validateApiRequest,
  validateQueryParams,
  sanitizeInput,
  sanitizeObject,
} from "../validation/schemas.ts";
import { isOk, isErr } from "../types/core.ts";

// Behavior: Regulation validation
Deno.test("Regulation validator should accept valid regulation data", () => {
  // Given valid regulation data
  const validRegulation = {
    id: "550e8400-e29b-41d4-a716-446655440000",
    country_code: "US",
    source_agency: "Department of Commerce",
    title: { en: "Export Control Regulation" },
    description: { en: "New export control measures for technology." },
    category: "technical_regulation",
    subcategory: "export_control",
    hs_codes: ["8542", "8541"],
    timeline: {
      effective_date: "2024-01-15",
      implementation_deadline: "2024-03-15",
    },
    impact_assessment: {
      economic: 8,
      operational: 7,
      compliance: 9,
      urgency: 8,
    },
    related_regulations: [],
    original_language: "en",
    document_metadata: {
      source_url: "https://example.com/document",
      document_hash: "abc123def456",
      content_type: "text/html",
      file_size: 15420,
      extracted_at: "2024-01-10T10:00:00Z",
    },
    version: 1,
    created_at: "2024-01-10T10:00:00Z",
    updated_at: "2024-01-10T10:00:00Z",
  };
  
  // When validating the regulation
  const result = validateRegulation(validRegulation);
  
  // Then validation should succeed
  assertEquals(isOk(result), true);
  
  // And return the validated data
  if (isOk(result)) {
    assertEquals(result.data.country_code, "US");
    assertEquals(result.data.category, "technical_regulation");
  }
});

Deno.test("Regulation validator should reject invalid country codes", () => {
  // Given regulation data with invalid country code
  const invalidRegulation = {
    id: "550e8400-e29b-41d4-a716-446655440000",
    country_code: "INVALID",
    source_agency: "Test Agency",
    title: { en: "Test Regulation" },
    description: { en: "Test description" },
    category: "tariff",
    subcategory: "test",
    hs_codes: ["1234"],
    timeline: { effective_date: "2024-01-15" },
    impact_assessment: { economic: 5, operational: 5, compliance: 5, urgency: 5 },
    related_regulations: [],
    original_language: "en",
    document_metadata: {
      source_url: "https://example.com",
      document_hash: "abc123",
      content_type: "text/html",
      file_size: 1000,
      extracted_at: "2024-01-10",
    },
    version: 1,
    created_at: "2024-01-10",
    updated_at: "2024-01-10",
  };
  
  // When validating the regulation
  const result = validateRegulation(invalidRegulation);
  
  // Then validation should fail
  assertEquals(isErr(result), true);
  
  // And indicate the country code error
  if (isErr(result)) {
    assertEquals(result.error.field, "country_code");
    assertExists(result.error.message);
  }
});

Deno.test("Regulation validator should reject missing required fields", () => {
  // Given regulation data missing required fields
  const incompleteRegulation = {
    country_code: "US",
    // Missing other required fields
  };
  
  // When validating the regulation
  const result = validateRegulation(incompleteRegulation);
  
  // Then validation should fail
  assertEquals(isErr(result), true);
  
  // And provide error information
  if (isErr(result)) {
    assertExists(result.error.field);
    assertExists(result.error.message);
  }
});

// Behavior: Change validation
Deno.test("Change validator should accept valid change data", () => {
  // Given valid change data
  const validChange = {
    id: "550e8400-e29b-41d4-a716-446655440000",
    regulation_id: "550e8400-e29b-41d4-a716-446655440001",
    change_type: "amendment",
    priority: "high",
    impact_score: { economic: 7, operational: 6, compliance: 8, urgency: 7 },
    summary: { en: "Regulation amended to include new provisions" },
    detailed_changes: [{
      field: "hs_codes",
      previous_value: ["8542"],
      new_value: ["8542", "8541"],
      change_description: "Added new HS code",
      confidence_score: 0.95,
    }],
    affected_industries: ["Technology", "Electronics"],
    implementation_date: "2024-03-15",
    stakeholders: ["importers", "exporters"],
    detected_at: "2024-01-12",
    notification_sent: false,
  };
  
  // When validating the change
  const result = validateChange(validChange);
  
  // Then validation should succeed
  assertEquals(isOk(result), true);
  
  // And return the validated data
  if (isOk(result)) {
    assertEquals(result.data.change_type, "amendment");
    assertEquals(result.data.priority, "high");
  }
});

Deno.test("Change validator should reject invalid priority values", () => {
  // Given change data with invalid priority
  const invalidChange = {
    id: "550e8400-e29b-41d4-a716-446655440000",
    regulation_id: "550e8400-e29b-41d4-a716-446655440001",
    change_type: "amendment",
    priority: "invalid_priority",
    impact_score: { economic: 7, operational: 6, compliance: 8, urgency: 7 },
    summary: { en: "Test change" },
    detailed_changes: [],
    affected_industries: [],
    implementation_date: "2024-03-15",
    stakeholders: [],
    detected_at: "2024-01-12",
    notification_sent: false,
  };
  
  // When validating the change
  const result = validateChange(invalidChange);
  
  // Then validation should fail
  assertEquals(isErr(result), true);
  
  // And indicate the priority error
  if (isErr(result)) {
    assertEquals(result.error.field, "priority");
  }
});

// Behavior: Filter validation
Deno.test("RegulationFilters validator should accept valid filter parameters", () => {
  // Given valid filter parameters
  const validFilters = {
    country_codes: ["US", "EU"],
    categories: ["tariff", "technical_regulation"],
    limit: 10,
    offset: 0,
  };
  
  // When validating the filters
  const result = validateRegulationFilters(validFilters);
  
  // Then validation should succeed
  assertEquals(isOk(result), true);
  
  // And return the validated filters
  if (isOk(result)) {
    assertEquals(result.data.country_codes?.length, 2);
    assertEquals(result.data.limit, 10);
  }
});

Deno.test("RegulationFilters validator should apply default values", () => {
  // Given minimal filter parameters
  const minimalFilters = {};
  
  // When validating the filters
  const result = validateRegulationFilters(minimalFilters);
  
  // Then validation should succeed
  assertEquals(isOk(result), true);
  
  // And apply default values
  if (isOk(result)) {
    assertEquals(result.data.limit, 10);
    assertEquals(result.data.offset, 0);
  }
});

// Behavior: API request validation
Deno.test("API request validator should handle valid JSON requests", async () => {
  // Given a valid JSON request
  const validData = {
    user_id: "test-user",
    name: "Test Subscription",
    country_codes: ["US"],
    regulation_categories: ["tariff"],
    priority_threshold: "medium",
    notification_config: {
      enabled: true,
      priority_threshold: "medium",
      channels: [],
      delay_minutes: 0,
      batch_notifications: false,
      custom_filters: [],
    },
    active: true,
  };
  
  const request = new Request("http://localhost/test", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(validData),
  });
  
  // When validating the API request
  const validator = validateApiRequest(validateSubscription);
  const result = await validator(request);
  
  // Then validation should succeed
  assertEquals(isOk(result), true);
});

Deno.test("API request validator should handle invalid JSON", async () => {
  // Given a request with invalid JSON
  const request = new Request("http://localhost/test", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: "invalid json",
  });
  
  // When validating the API request
  const validator = validateApiRequest(validateSubscription);
  const result = await validator(request);
  
  // Then validation should fail
  assertEquals(isErr(result), true);
  
  // And return an error response
  if (isErr(result)) {
    assertEquals(result.error.status, 400);
  }
});

// Behavior: Query parameter validation
Deno.test("Query parameter validator should parse and validate URL parameters", () => {
  // Given a URL with query parameters
  const url = new URL("http://localhost/api/regulations?country_codes=US&limit=5&offset=10");
  
  // When validating query parameters
  const result = validateQueryParams(url, validateRegulationFilters);
  
  // Then validation should succeed
  assertEquals(isOk(result), true);
  
  // And parse the parameters correctly
  if (isOk(result)) {
    assertEquals(result.data.limit, 5);
    assertEquals(result.data.offset, 10);
  }
});

Deno.test("Query parameter validator should handle boolean conversion", () => {
  // Given a URL with boolean parameters
  const url = new URL("http://localhost/test?active=true&enabled=false");
  
  // When validating with a validator that expects booleans
  const booleanValidator = (data: unknown) => {
    if (typeof data !== "object" || data === null) {
      return { success: false as const, error: { field: "data", message: "Must be object", code: "VALIDATION_ERROR" } };
    }
    const obj = data as Record<string, unknown>;
    if (typeof obj.active !== "boolean" || typeof obj.enabled !== "boolean") {
      return { success: false as const, error: { field: "boolean", message: "Must be boolean", code: "VALIDATION_ERROR" } };
    }
    return { success: true as const, data: obj };
  };
  
  const result = validateQueryParams(url, booleanValidator);
  
  // Then boolean conversion should work
  assertEquals(isOk(result), true);
  
  if (isOk(result)) {
    assertEquals(result.data.active, true);
    assertEquals(result.data.enabled, false);
  }
});

// Behavior: Sanitization
Deno.test("Input sanitizer should remove potentially harmful content", () => {
  // Given input with potential security issues
  const dangerousInput = "<script>alert('xss')</script>Hello World";
  
  // When sanitizing the input
  const sanitized = sanitizeInput(dangerousInput);
  
  // Then harmful content should be removed
  assertEquals(sanitized.includes("<script>"), false);
  assertEquals(sanitized.includes("</script>"), false);
  assertEquals(sanitized.includes("Hello World"), true);
});

Deno.test("Input sanitizer should limit input length", () => {
  // Given very long input
  const longInput = "a".repeat(2000);
  
  // When sanitizing the input
  const sanitized = sanitizeInput(longInput);
  
  // Then length should be limited
  assertEquals(sanitized.length, 1000);
});

Deno.test("Object sanitizer should sanitize nested object properties", () => {
  // Given an object with potentially dangerous content
  const dangerousObject = {
    title: "<script>alert('xss')</script>Clean Title",
    description: "Safe description",
    nested: {
      field: "<b>Bold text</b>",
    },
    number: 42,
  };
  
  // When sanitizing the object
  const sanitized = sanitizeObject(dangerousObject);
  
  // Then string fields should be sanitized
  assertEquals(sanitized.title.includes("<script>"), false);
  assertEquals(sanitized.title.includes("Clean Title"), true);
  assertEquals(sanitized.nested.field.includes("<b>"), false);
  assertEquals(sanitized.nested.field.includes("Bold text"), true);
  
  // And non-string fields should remain unchanged
  assertEquals(sanitized.number, 42);
});

// Behavior: Error messages
Deno.test("Validators should provide meaningful error messages", () => {
  // Given invalid data
  const invalidData = {
    country_code: "TOOLONG",
    impact_assessment: {
      economic: 15, // Out of range
    },
  };
  
  // When validating
  const result = validateRegulation(invalidData);
  
  // Then error message should be meaningful
  assertEquals(isErr(result), true);
  
  if (isErr(result)) {
    assertExists(result.error.message);
    assertExists(result.error.field);
    assertExists(result.error.code);
  }
});