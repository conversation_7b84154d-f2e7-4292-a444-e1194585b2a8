// Default web sources for regulation monitoring
import type { WebSource } from "../services/firecrawl.ts";

export const DEFAULT_WEB_SOURCES: readonly WebSource[] = [
  {
    id: "us-trade-gov",
    name: "US Trade.gov News",
    url: "https://www.trade.gov/news",
    country: "US",
    agency: "Department of Commerce",
    category: ["trade_remedies", "export_control"],
    crawlConfig: {
      maxDepth: 2,
      limit: 5,
    },
    schedule: "0 */6 * * *",
    active: true,
  },
  // {
  //   id: "eu-trade-commission",
  //   name: "EU Trade Commission News",
  //   url: "https://policy.trade.ec.europa.eu/news",
  //   country: "EU",
  //   agency: "European Commission",
  //   category: ["trade_policy", "technical_regulation"],
  //   crawlConfig: {
  //     maxDepth: 2,
  //     limit: 5,
  //   },
  //   schedule: "0 */6 * * *",
  //   active: true,
  // },
  // {
  //   id: "wto-news",
  //   name: "WTO News and Updates",
  //   url: "https://www.wto.org/english/news_e/news_e.htm",
  //   country: "INTL",
  //   agency: "World Trade Organization",
  //   category: ["multilateral_trade", "dispute_settlement"],
  //   crawlConfig: {
  //     maxDepth: 2,
  //     limit: 5,
  //   },
  //   schedule: "0 */12 * * *",
  //   active: true,
  // },
  // {
  //   id: "uk-gov-trade",
  //   name: "UK Government Trade News",
  //   url: "https://www.gov.uk/government/organisations/department-for-international-trade/news",
  //   country: "GB",
  //   agency: "Department for International Trade",
  //   category: ["trade_policy", "export_control"],
  //   crawlConfig: {
  //     maxDepth: 2,
  //     limit: 5,
  //   },
  //   schedule: "0 */6 * * *",
  //   active: true,
  // },
];

export const getWebSources = (filters: {
  active?: boolean;
  country?: string | null
} = {}): WebSource[] => {
  return DEFAULT_WEB_SOURCES.filter(source => {
    if (filters.active !== undefined && source.active !== filters.active) {
      return false;
    }
    if (filters.country && source.country !== filters.country) {
      return false;
    }
    return true;
  });
};

export const getWebSourceById = (id: string): WebSource | null => {
  return DEFAULT_WEB_SOURCES.find(s => s.id === id) || null;
};

export const getActiveWebSources = (): readonly WebSource[] => {
  return DEFAULT_WEB_SOURCES.filter(source => source.active);
};